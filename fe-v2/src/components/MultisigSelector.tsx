import React, { useEffect } from 'react';
import { useMultisigAccounts } from '@/hooks/useMultisigAccounts';
import { RefreshCw, Users, Shield, Wallet } from 'lucide-react';
import { MultisigInfo } from '@/services/types';

interface MultisigInfoDisplayProps {
  onMultisigLoaded?: (multisig: MultisigInfo | null) => void;
  onError?: (error: string) => void;
}

const MultisigInfoDisplay: React.FC<MultisigInfoDisplayProps> = ({
  onMultisigLoaded,
  onError
}) => {
  const { multisigs, loading, error } = useMultisigAccounts();

  // 当多签信息加载完成时，自动选择第一个（通常只有一个）
  useEffect(() => {
    if (!loading && !error) {
      if (multisigs.length > 0) {
        onMultisigLoaded?.(multisigs[0]);
      } else {
        onMultisigLoaded?.(null);
      }
    }
    if (error) {
      onError?.(error);
    }
  }, [multisigs, loading, error, onMultisigLoaded, onError]);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 8)}...${address.slice(-4)}`;
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        多签账户信息
      </label>

      {loading && (
        <div className="flex items-center gap-2 text-gray-500 text-sm p-3 border border-gray-200 rounded-lg">
          <RefreshCw size={16} className="animate-spin" />
          正在加载多签账户信息...
        </div>
      )}

      {error && (
        <div className="p-3 border border-red-200 bg-red-50 rounded-lg">
          <div className="text-red-600 text-sm">
            {error}
          </div>
        </div>
      )}

      {!loading && !error && (
        <>
          {multisigs.length === 0 ? (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Users size={48} className="mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500 mb-2">未配置多签账户</p>
              <p className="text-sm text-gray-400">
                请联系管理员配置多签账户地址
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {multisigs.map((account) => (
                <div
                  key={account.address}
                  className="p-3 border border-blue-200 bg-blue-50 rounded-lg"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Wallet size={16} className="text-blue-600" />
                      <span className="font-medium text-sm text-blue-900">
                        {formatAddress(account.address)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-blue-700">
                      <Shield size={12} />
                      <span>{account.threshold}/{account.members.length}</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-xs text-blue-600">
                    <div>
                      <span className="text-blue-500">交易索引:</span> {account.transactionIndex}
                    </div>
                    <div>
                      <span className="text-blue-500">金库余额:</span> {account.vault.balanceSOL.toFixed(4)} SOL
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default MultisigInfoDisplay;
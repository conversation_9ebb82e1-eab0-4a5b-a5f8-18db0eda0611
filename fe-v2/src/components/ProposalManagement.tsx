import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { Vote, CheckCircle, XCircle, Clock, Play, RefreshCw, AlertCircle } from 'lucide-react';
import MultisigSelector from './MultisigSelector';
import Api from '@/services/api';
import { Transaction } from '@/services/types';

interface ProposalManagementProps {
  onSuccess?: (signature: string) => void;
  onError?: (error: string) => void;
}

const ProposalManagement: React.FC<ProposalManagementProps> = ({ onSuccess, onError }) => {
  const { publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const [selectedMultisig, setSelectedMultisig] = useState('');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string>('');
  const [status, setStatus] = useState<string>('');

  // 使用useRef存储回调函数，避免依赖变化
  const onErrorRef = useRef(onError);
  const onSuccessRef = useRef(onSuccess);

  // 更新ref
  useEffect(() => {
    onErrorRef.current = onError;
    onSuccessRef.current = onSuccess;
  }, [onError, onSuccess]);

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleCreateNew = () => {
    onErrorRef.current?.('创建新多签账户功能将在后续步骤中实现');
  };

  const loadTransactions = useCallback(async () => {
    if (!selectedMultisig || !publicKey) return;

    setLoading(true);
    setStatus('正在加载交易...');

    try {
      const result = await Api.getTransactions(selectedMultisig);
      setTransactions(result.transactions);
      setStatus('');
    } catch (error: any) {
      console.error('加载交易失败:', error);
      onErrorRef.current?.(error.message || '加载交易失败');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [selectedMultisig, publicKey]);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  const handleVote = useCallback(async (transactionIndex: number, vote: 'approve' | 'reject') => {
    if (!signTransaction || !publicKey || !selectedMultisig) return;

    try {
      setActionLoading(`${vote}-${transactionIndex}`);

      // 构建投票指令
      const multisigPda = new PublicKey(selectedMultisig);
      const instruction = vote === 'approve'
        ? multisig.instructions.proposalApprove({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          })
        : multisig.instructions.proposalReject({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 提交投票
      const result = await Api.voteTransaction({
        multisigAddress: selectedMultisig,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
        vote,
      });

      onSuccessRef.current?.(result.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('投票失败:', error);
      onErrorRef.current?.(error.message || '投票失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, selectedMultisig, loadTransactions]);

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !selectedMultisig) return;

    try {
      setActionLoading(`execute-${transactionIndex}`);

      // 获取执行指令
      const result = await Api.buildExecuteInstruction({
        multisigAddress: selectedMultisig,
        transactionIndex,
        executorPublicKey: publicKey.toBase58(),
      });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [result.instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 执行交易
      const executeResult = await Api.executeTransaction({
        multisigAddress: selectedMultisig,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
      });

      onSuccessRef.current?.(executeResult.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('执行交易失败:', error);
      onErrorRef.current?.(error.message || '执行交易失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, selectedMultisig, loadTransactions]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved':
        return 'text-green-500';
      case 'Rejected':
        return 'text-red-500';
      case 'Executed':
        return 'text-blue-500';
      case 'Cancelled':
        return 'text-gray-500';
      default:
        return 'text-yellow-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'Rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'Executed':
        return <Play className="w-5 h-5 text-blue-500" />;
      case 'Cancelled':
        return <XCircle className="w-5 h-5 text-gray-500" />;
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">交易管理</h2>

        <div className="mb-6">
          <MultisigSelector
            selectedMultisig={selectedMultisig}
            onSelect={setSelectedMultisig}
            onCreateNew={handleCreateNew}
          />
        </div>

        {status && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-700 text-sm">{status}</p>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载中...</span>
          </div>
        ) : transactions.length > 0 ? (
          <div className="space-y-4">
            {transactions.map((tx) => (
              <div
                key={tx.transactionIndex}
                className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(tx.status)}
                    <span className={`font-medium ${getStatusColor(tx.status)}`}>
                      #{tx.transactionIndex}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {tx.approvals}/{tx.threshold} 票
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="text-sm text-gray-600">
                    创建者: {formatAddress(tx.creator)}
                  </div>
                  {tx.memo && (
                    <div className="text-sm text-gray-600">
                      备注: {tx.memo}
                    </div>
                  )}
                </div>

                {tx.status === 'Active' && publicKey && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleVote(tx.transactionIndex, 'approve')}
                      disabled={actionLoading === `approve-${tx.transactionIndex}`}
                      className="flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
                    >
                      <Vote className="w-4 h-4 mr-2" />
                      同意
                    </button>
                    <button
                      onClick={() => handleVote(tx.transactionIndex, 'reject')}
                      disabled={actionLoading === `reject-${tx.transactionIndex}`}
                      className="flex items-center px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      反对
                    </button>
                  </div>
                )}

                {tx.canExecute && publicKey && (
                  <button
                    onClick={() => handleExecute(tx.transactionIndex)}
                    disabled={actionLoading === `execute-${tx.transactionIndex}`}
                    className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 mt-2"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    执行
                  </button>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Vote className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>暂无交易记录</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProposalManagement;
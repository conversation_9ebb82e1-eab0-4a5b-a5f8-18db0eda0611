import React, { useState, useCallback, useEffect } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { Users, AlertCircle, User, Shield, Key } from 'lucide-react';
import MultisigSelector from './MultisigSelector';
import Api from '@/services/api';
import { MultisigInfo } from '@/services/types';

interface MemberManagementProps {
  onError?: (error: string) => void;
}

const MemberManagement: React.FC<MemberManagementProps> = ({ onError }) => {
  const { publicKey } = useWallet();
  const [selectedMultisig, setSelectedMultisig] = useState('');
  const [multisigInfo, setMultisigInfo] = useState<MultisigInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<string>('');

  const handleCreateNew = () => {
    onError?.('创建新多签账户功能将在后续步骤中实现');
  };

  const loadMultisigInfo = useCallback(async () => {
    if (!selectedMultisig) {
      setMultisigInfo(null);
      return;
    }

    try {
      setLoading(true);
      setStatus('获取多签信息...');

      const result = await Api.getMultisigs();
      const info = result.multisigs.find(m => m.address === selectedMultisig);

      if (!info) {
        throw new Error('未找到指定的多签账户');
      }

      setMultisigInfo(info);
      setStatus('');
    } catch (error: any) {
      console.error('获取多签信息失败:', error);
      onError?.(error.message || '获取多签信息失败');
      setMultisigInfo(null);
    } finally {
      setLoading(false);
    }
  }, [selectedMultisig, onError]);

  useEffect(() => {
    loadMultisigInfo();
  }, [loadMultisigInfo]);

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const isCurrentUser = (memberKey: string) => {
    return publicKey?.toBase58() === memberKey;
  };

  const getCurrentUserRole = () => {
    if (!multisigInfo || !publicKey) return null;

    const userMember = multisigInfo.members.find(member =>
      member.key === publicKey.toBase58()
    );

    if (!userMember) return null;

    // 检查是否是创建者
    if (multisigInfo.createKey === publicKey.toBase58()) {
      return 'Creator';
    }

    return 'Member';
  };

  return (
    <div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '24px' }}>
        <Users size={24} color="#007bff" />
        <h2 style={{ margin: 0, color: '#333' }}>成员管理</h2>
      </div>

      <MultisigSelector
        selectedMultisig={selectedMultisig}
        onSelect={setSelectedMultisig}
        onCreateNew={handleCreateNew}
      />

      {status && (
        <div style={{
          padding: '12px',
          background: '#f0f8ff',
          borderRadius: '6px',
          color: '#007bff',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <AlertCircle size={16} />
          {status}
        </div>
      )}

      {multisigInfo && (
        <div style={{ marginTop: '24px' }}>
          <div style={{
            background: '#f8f9fa',
            padding: '16px',
            borderRadius: '8px',
            marginBottom: '24px'
          }}>
            <h3 style={{ margin: '0 0 16px 0', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Shield size={20} />
              多签配置
            </h3>
            <div style={{ display: 'grid', gap: '12px' }}>
              <div>
                <strong>阈值：</strong>
                {multisigInfo.threshold} / {multisigInfo.members.length}
              </div>
              <div>
                <strong>当前角色：</strong>
                {getCurrentUserRole() || '非成员'}
              </div>
            </div>
          </div>

          <div>
            <h3 style={{ margin: '0 0 16px 0', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <User size={20} />
              成员列表
            </h3>
            <div style={{ display: 'grid', gap: '12px' }}>
              {multisigInfo.members.map((member) => (
                <div
                  key={member.key}
                  style={{
                    padding: '12px',
                    background: isCurrentUser(member.key) ? '#f0f8ff' : '#fff',
                    border: '1px solid #e1e4e8',
                    borderRadius: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <User size={16} />
                    <code>{formatAddress(member.key, 12)}</code>
                    {member.key === multisigInfo.createKey && (
                      <span style={{
                        background: '#52c41a',
                        color: '#fff',
                        padding: '2px 8px',
                        borderRadius: '12px',
                        fontSize: '12px'
                      }}>
                        创建者
                      </span>
                    )}
                    {isCurrentUser(member.key) && (
                      <span style={{
                        background: '#1890ff',
                        color: '#fff',
                        padding: '2px 8px',
                        borderRadius: '12px',
                        fontSize: '12px'
                      }}>
                        当前用户
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemberManagement;
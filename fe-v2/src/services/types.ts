export interface MultisigMember {
  key: string;
  permissions: any;
}

export interface TokenAccount {
  address: string;
  mint: string;
  name: string;
  balance: number;
  decimals: number;
  uiAmount: number;
  uiAmountString: string;
}

export interface Recipient {
  name: string;
  address: string;
  description: string;
}

export interface VaultInfo {
  address: string;
  balance: number;
  balanceSOL: number;
  tokenAccounts: TokenAccount[];
}

export interface MultisigInfo {
  address: string;
  members: MultisigMember[];
  threshold: number;
  transactionIndex: number;
  createKey: string;
  allowExternalExecute: boolean;
  rentCollector?: string;
  vault: VaultInfo;
}

export interface Transaction {
  transactionIndex: number;
  status: 'Active' | 'Executed' | 'Rejected' | 'Cancelled' | 'Approved';
  approvals: number;
  threshold: number;
  creator: string;
  memo?: string;
  votes: Array<{
    member: string;
    vote: 'Approve' | 'Reject';
  }>;
  canExecute: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
import { MultisigInfo, Transaction, TokenAccount } from '../types';

const API_BASE_URL = 'http://localhost:3001';

class Api {
  private static async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // ==================== 系统接口 ====================

  static async healthCheck() {
    return this.request<{ status: string; timestamp: string }>('/health');
  }

  static async getBlockhash() {
    return this.request<{ blockhash: string }>('/api/blockhash');
  }

  // ==================== 账户信息接口 ====================

  static async getMultisigs() {
    return this.request<{ multisigs: MultisigInfo[] }>('/api/multisigs');
  }

  // ==================== 转账接口 ====================

  static async createSolTransfer(params: {
    multisigAddress: string;
    recipientAddress: string;
    amount: string;
    creatorPublicKey: string;
    signedTransaction: string;
  }) {
    return this.request<{ signature: string }>('/api/transfer', {
      method: 'POST',
      body: JSON.stringify({ ...params, type: 'sol' }),
    });
  }

  static async createTokenTransfer(params: {
    multisigAddress: string;
    recipientAddress: string;
    tokenMint: string;
    amount: string;
    decimals: number;
    creatorPublicKey: string;
    signedTransaction: string;
  }) {
    return this.request<{ signature: string }>('/api/transfer', {
      method: 'POST',
      body: JSON.stringify({ ...params, type: 'token' }),
    });
  }

  // ==================== 交易管理接口 ====================

  static async getTransactions(multisigAddress: string) {
    return this.request<{ transactions: Transaction[] }>('/api/transactions', {
      method: 'POST',
      body: JSON.stringify({ multisigAddress }),
    });
  }

  static async buildExecuteInstruction(params: {
    multisigAddress: string;
    transactionIndex: number;
    executorPublicKey: string;
  }) {
    return this.request<{
      instruction: {
        keys: Array<{
          pubkey: string;
          isSigner: boolean;
          isWritable: boolean;
        }>;
        programId: string;
        data: number[];
      };
      lookupTableAccounts: any[];
    }>('/api/transactions/build-execute', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  static async voteTransaction(params: {
    multisigAddress: string;
    transactionIndex: number;
    userPublicKey: string;
    signedTransaction: string;
    vote: 'approve' | 'reject';
  }) {
    return this.request<{ signature: string }>('/api/transactions/vote', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  static async executeTransaction(params: {
    multisigAddress: string;
    transactionIndex: number;
    userPublicKey: string;
    signedTransaction: string;
  }) {
    return this.request<{ signature: string }>('/api/transactions/execute', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }
}

export default Api;
import React, { useState, useCallback, useEffect } from 'react';
import { Table, Button, Tag, message, Space } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { CheckCircle, XCircle, Clock, Play } from 'lucide-react';
import { useMultisig } from '@/contexts/MultisigContext';
import Api from '@/services/api/index';
import { Transaction } from '@/services/types';
import type { ColumnsType } from 'antd/es/table';

const TransactionsPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { publicKey, signTransaction } = useWallet();
  const { currentMultisig } = useMultisig();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string>('');

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  const loadTransactions = useCallback(async () => {
    if (!currentMultisig || !publicKey) return;

    setLoading(true);

    try {
      const result = await Api.getTransactions(currentMultisig.address);
      setTransactions(result.transactions);
    } catch (error: any) {
      console.error('加载交易失败:', error);
      handleError(error.message || '加载交易失败');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [currentMultisig, publicKey]);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  const handleVote = useCallback(async (transactionIndex: number, vote: 'approve' | 'reject') => {
    if (!signTransaction || !publicKey || !currentMultisig) return;

    try {
      setActionLoading(`${vote}-${transactionIndex}`);

      // 构建投票指令
      const multisigPda = new PublicKey(currentMultisig.address);
      const instruction = vote === 'approve'
        ? multisig.instructions.proposalApprove({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          })
        : multisig.instructions.proposalReject({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 提交投票
      const result = await Api.voteTransaction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
        vote,
      });

      handleSuccess(result.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('投票失败:', error);
      handleError(error.message || '投票失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions]);

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !currentMultisig) return;

    try {
      setActionLoading(`execute-${transactionIndex}`);

      // 获取执行指令
      const result = await Api.buildExecuteInstruction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        executorPublicKey: publicKey.toBase58(),
      });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 转换指令格式
      const instruction = {
        keys: result.instruction.keys.map(key => ({
          pubkey: new PublicKey(key.pubkey),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: new PublicKey(result.instruction.programId),
        data: Buffer.from(result.instruction.data),
      };

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 执行交易
      const executeResult = await Api.executeTransaction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
      });

      handleSuccess(executeResult.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('执行交易失败:', error);
      handleError(error.message || '执行交易失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions]);

  // 获取交易类型
  const getTransactionType = (memo?: string) => {
    if (!memo) return '未知';
    if (memo.includes('Transfer')) return '转账';
    return '其他';
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // 表格列定义
  const columns: ColumnsType<Transaction> = [
    {
      title: '交易ID',
      dataIndex: 'transactionIndex',
      key: 'transactionIndex',
      width: 80,
      render: (index: number) => `#${index}`,
    },
    {
      title: '类型',
      dataIndex: 'memo',
      key: 'type',
      width: 80,
      render: (memo: string) => getTransactionType(memo),
    },
    {
      title: '金额',
      dataIndex: 'memo',
      key: 'amount',
      width: 120,
      render: (memo: string) => {
        if (!memo) return '-';
        const match = memo.match(/Transfer\s+([\d.]+)\s+(\w+)/);
        if (match) {
          return `${match[1]} ${match[2]}`;
        }
        return '-';
      },
    },
    {
      title: 'To地址',
      dataIndex: 'memo',
      key: 'toAddress',
      width: 120,
      render: (memo: string) => {
        if (!memo) return '-';
        const match = memo.match(/to\s+([A-Za-z0-9]{8})\.\.\./);
        if (match) {
          return `${match[1]}...`;
        }
        return '-';
      },
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
      width: 100,
      render: (creator: string) => formatAddress(creator),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        let color = 'default';
        let text = status;

        switch (status) {
          case 'Active':
            color = 'processing';
            text = '待投票';
            break;
          case 'Approved':
            color = 'success';
            text = '已批准';
            break;
          case 'Rejected':
            color = 'error';
            text = '已拒绝';
            break;
          case 'Executed':
            color = 'success';
            text = '已执行';
            break;
          case 'Cancelled':
            color = 'default';
            text = '已取消';
            break;
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '投票进度',
      key: 'progress',
      width: 100,
      render: (_, record: Transaction) => `${record.approvals}/${record.threshold}`,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: Transaction) => {
        if (!publicKey) return null;

        if (record.status === 'Active') {
          return (
            <Space>
              <Button
                type="primary"
                size="small"
                loading={actionLoading === `approve-${record.transactionIndex}`}
                onClick={() => handleVote(record.transactionIndex, 'approve')}
                icon={<CheckCircle size={14} />}
              >
                同意
              </Button>
              <Button
                danger
                size="small"
                loading={actionLoading === `reject-${record.transactionIndex}`}
                onClick={() => handleVote(record.transactionIndex, 'reject')}
                icon={<XCircle size={14} />}
              >
                拒绝
              </Button>
            </Space>
          );
        }

        if (record.canExecute) {
          return (
            <Button
              type="primary"
              size="small"
              loading={actionLoading === `execute-${record.transactionIndex}`}
              onClick={() => handleExecute(record.transactionIndex)}
              icon={<Play size={14} />}
            >
              执行
            </Button>
          );
        }

        return <span style={{ color: '#999' }}>无操作</span>;
      },
    },
  ];

  return (
    <>
      {contextHolder}
      <div>
        <Table
          columns={columns}
          dataSource={transactions}
          loading={loading}
          rowKey="transactionIndex"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center', color: '#999' }}>
                <Clock size={48} style={{ marginBottom: '16px', opacity: 0.3 }} />
                <div>暂无交易记录</div>
              </div>
            ),
          }}
        />
      </div>
    </>
  );
};

export default TransactionsPage;
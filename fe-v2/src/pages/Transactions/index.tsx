import React, { useState, useCallback, useEffect } from 'react';
import { Card, message, Alert } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { Vote, CheckCircle, XCircle, Clock, Play, RefreshCw, Wallet, Shield } from 'lucide-react';
import { useMultisig } from '@/contexts/MultisigContext';
import Api from '@/services/api/index';
import { Transaction } from '@/services/types';

const TransactionsPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { publicKey, signTransaction } = useWallet();
  const { currentMultisig, loading: multisigLoading, error: multisigError } = useMultisig();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string>('');
  const [status, setStatus] = useState<string>('');

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  const loadTransactions = useCallback(async () => {
    if (!currentMultisig || !publicKey) return;

    setLoading(true);
    setStatus('正在加载交易...');

    try {
      const result = await Api.getTransactions(currentMultisig.address);
      setTransactions(result.transactions);
      setStatus('');
    } catch (error: any) {
      console.error('加载交易失败:', error);
      handleError(error.message || '加载交易失败');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [currentMultisig, publicKey]);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  const handleVote = useCallback(async (transactionIndex: number, vote: 'approve' | 'reject') => {
    if (!signTransaction || !publicKey || !currentMultisig) return;

    try {
      setActionLoading(`${vote}-${transactionIndex}`);

      // 构建投票指令
      const multisigPda = new PublicKey(currentMultisig.address);
      const instruction = vote === 'approve'
        ? multisig.instructions.proposalApprove({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          })
        : multisig.instructions.proposalReject({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 提交投票
      const result = await Api.voteTransaction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
        vote,
      });

      handleSuccess(result.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('投票失败:', error);
      handleError(error.message || '投票失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions]);

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !currentMultisig) return;

    try {
      setActionLoading(`execute-${transactionIndex}`);

      // 获取执行指令
      const result = await Api.buildExecuteInstruction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        executorPublicKey: publicKey.toBase58(),
      });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 转换指令格式
      const instruction = {
        keys: result.instruction.keys.map(key => ({
          pubkey: new PublicKey(key.pubkey),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: new PublicKey(result.instruction.programId),
        data: Buffer.from(result.instruction.data),
      };

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 执行交易
      const executeResult = await Api.executeTransaction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
      });

      handleSuccess(executeResult.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('执行交易失败:', error);
      handleError(error.message || '执行交易失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved':
        return 'text-green-500';
      case 'Rejected':
        return 'text-red-500';
      case 'Executed':
        return 'text-blue-500';
      case 'Cancelled':
        return 'text-gray-500';
      default:
        return 'text-yellow-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'Rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'Executed':
        return <Play className="w-5 h-5 text-blue-500" />;
      case 'Cancelled':
        return <XCircle className="w-5 h-5 text-gray-500" />;
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  return (
    <>
      {contextHolder}
      <div style={{ padding: '24px' }}>
        <Card>
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">交易管理</h2>

              {/* 多签信息显示 */}
              <div className="mb-6">
                {multisigLoading && (
                  <Alert
                    message="正在加载多签账户信息..."
                    type="info"
                    showIcon
                    icon={<RefreshCw className="w-4 h-4 animate-spin" />}
                  />
                )}

                {multisigError && (
                  <Alert
                    message="加载多签信息失败"
                    description={multisigError}
                    type="error"
                    showIcon
                  />
                )}

                {!multisigLoading && !multisigError && currentMultisig && (
                  <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Wallet className="w-5 h-5 text-blue-600" />
                        <span className="font-medium text-blue-900">
                          多签账户: {formatAddress(currentMultisig.address)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-blue-700">
                        <Shield className="w-4 h-4" />
                        <span>{currentMultisig.threshold}/{currentMultisig.members.length}</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-blue-600">
                      <div>
                        <span className="text-blue-500">交易索引:</span> {currentMultisig.transactionIndex}
                      </div>
                      <div>
                        <span className="text-blue-500">金库余额:</span> {currentMultisig.vault.balanceSOL.toFixed(4)} SOL
                      </div>
                    </div>
                  </div>
                )}

                {!multisigLoading && !multisigError && !currentMultisig && (
                  <Alert
                    message="未配置多签账户"
                    description="请联系管理员配置多签账户地址"
                    type="warning"
                    showIcon
                  />
                )}
              </div>

              {status && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-blue-700 text-sm">{status}</p>
                </div>
              )}

              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin text-blue-500 mr-2" />
                  <span className="text-gray-600">加载中...</span>
                </div>
              ) : transactions.length > 0 ? (
                <div className="space-y-4">
                  {transactions.map((tx) => (
                    <div
                      key={tx.transactionIndex}
                      className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(tx.status)}
                          <span className={`font-medium ${getStatusColor(tx.status)}`}>
                            #{tx.transactionIndex}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {tx.approvals}/{tx.threshold} 票
                        </div>
                      </div>

                      <div className="space-y-2 mb-4">
                        <div className="text-sm text-gray-600">
                          创建者: {formatAddress(tx.creator)}
                        </div>
                        {tx.memo && (
                          <div className="text-sm text-gray-600">
                            备注: {tx.memo}
                          </div>
                        )}
                      </div>

                      {tx.status === 'Active' && publicKey && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleVote(tx.transactionIndex, 'approve')}
                            disabled={actionLoading === `approve-${tx.transactionIndex}`}
                            className="flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
                          >
                            <Vote className="w-4 h-4 mr-2" />
                            同意
                          </button>
                          <button
                            onClick={() => handleVote(tx.transactionIndex, 'reject')}
                            disabled={actionLoading === `reject-${tx.transactionIndex}`}
                            className="flex items-center px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50"
                          >
                            <XCircle className="w-4 h-4 mr-2" />
                            反对
                          </button>
                        </div>
                      )}

                      {tx.canExecute && publicKey && (
                        <button
                          onClick={() => handleExecute(tx.transactionIndex)}
                          disabled={actionLoading === `execute-${tx.transactionIndex}`}
                          className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 mt-2"
                        >
                          <Play className="w-4 h-4 mr-2" />
                          执行
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Vote className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>暂无交易记录</p>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
    </>
  );
};

export default TransactionsPage;
import React, { useState, useCallback } from 'react';
import { Form, Input, Button, Card, Space, message, Alert } from 'antd';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createTransferInstruction } from '@solana/spl-token';
import * as multisig from '@sqds/multisig';
import { SendOutlined } from '@ant-design/icons';
import { RefreshCw, Wallet, Shield } from 'lucide-react';
import { useMultisig } from '@/contexts/MultisigContext';
import Api from '@/services/api/index';

// 常用Token列表（主网）
const commonTokens = [
  { name: 'USDC', mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', decimals: 6 },
];

const SendPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const { currentMultisig, loading: multisigLoading, error: multisigError } = useMultisig();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  const validateInputs = (values: any) => {
    if (!publicKey) {
      throw new Error('请先连接钱包');
    }
    if (!currentMultisig) {
      throw new Error('多签账户未加载');
    }
    if (!values.recipient) {
      throw new Error('请输入接收地址');
    }
    if (!values.amount || parseFloat(values.amount) <= 0) {
      throw new Error('请输入有效的转账金额');
    }

    // 验证地址格式
    try {
      new PublicKey(currentMultisig.address);
    } catch {
      throw new Error('多签地址格式不正确');
    }

    try {
      new PublicKey(values.recipient);
    } catch {
      throw new Error('接收地址格式不正确');
    }

    if (values.tokenMint) {
      try {
        new PublicKey(values.tokenMint);
      } catch {
        throw new Error('Token合约地址格式不正确');
      }
    }
  };

  const handleTransfer = useCallback(async (values: any) => {
    if (!signTransaction) {
      handleError('钱包不支持签名功能');
      return;
    }

    try {
      setLoading(true);
      validateInputs(values);

      const multisigPda = new PublicKey(currentMultisig!.address);
      const recipientPubkey = new PublicKey(values.recipient);

      // 使用当前多签信息
      const multisigInfo = currentMultisig!;

      // 检查当前用户是否是多签成员
      const isMember = multisigInfo.members.some(member => member.key === publicKey!.toBase58());
      if (!isMember) {
        throw new Error('当前钱包不是多签成员');
      }

      const vaultPda = new PublicKey(multisigInfo.vault.address);
      let transferInstruction;
      const transferAmount = values.tokenMint
        ? Math.floor(parseFloat(values.amount) * Math.pow(10, values.decimals || 6))
        : Math.floor(parseFloat(values.amount) * LAMPORTS_PER_SOL);

      if (values.tokenMint) {
        // Token 转账
        const tokenMintPubkey = new PublicKey(values.tokenMint);
        const vaultTokenAccount = await getAssociatedTokenAddress(
          tokenMintPubkey,
          vaultPda,
          true
        );
        const recipientTokenAccount = await getAssociatedTokenAddress(
          tokenMintPubkey,
          recipientPubkey
        );

        // 检查金库Token账户余额
        const tokenAccount = multisigInfo.vault.tokenAccounts.find(
          t => t.mint === values.tokenMint
        );
        if (!tokenAccount || tokenAccount.balance < transferAmount) {
          throw new Error(`Token余额不足。当前余额: ${tokenAccount ? (tokenAccount.balance / Math.pow(10, values.decimals || 6)).toFixed(values.decimals || 6) : 0} Token`);
        }

        transferInstruction = createTransferInstruction(
          vaultTokenAccount,
          recipientTokenAccount,
          vaultPda,
          transferAmount,
          [],
          TOKEN_PROGRAM_ID
        );
      } else {
        // SOL 转账
        if (multisigInfo.vault.balance < transferAmount) {
          throw new Error(`SOL余额不足。当前余额: ${multisigInfo.vault.balanceSOL} SOL`);
        }

        transferInstruction = SystemProgram.transfer({
          fromPubkey: vaultPda,
          toPubkey: recipientPubkey,
          lamports: transferAmount,
        });
      }

      const { blockhash } = await Api.getBlockhash();
      const transferMessage = new TransactionMessage({
        payerKey: vaultPda,
        recentBlockhash: blockhash,
        instructions: [transferInstruction],
      });

      const nextTransactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

      // 创建交易并投票
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: `Transfer ${values.amount} ${values.tokenMint ? 'Token' : 'SOL'} to ${values.recipient.substring(0, 8)}...`,
        programId,
      });

      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        isDraft: false,
        programId,
      });

      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        member: publicKey!,
        programId,
      });

      const combinedMessage = new TransactionMessage({
        payerKey: publicKey!,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message());
      const signedTx = await signTransaction(combinedTx);

      // 发送交易
      const result = values.tokenMint
        ? await Api.createTokenTransfer({
            multisigAddress: currentMultisig!.address,
            recipientAddress: values.recipient,
            tokenMint: values.tokenMint,
            amount: values.amount,
            decimals: values.decimals || 6,
            creatorPublicKey: publicKey!.toBase58(),
            signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
          })
        : await Api.createSolTransfer({
            multisigAddress: currentMultisig!.address,
            recipientAddress: values.recipient,
            amount: values.amount,
            creatorPublicKey: publicKey!.toBase58(),
            signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
          });

      messageApi.success('转账交易创建成功！等待其他成员投票...');
      handleSuccess(result.signature);

    } catch (error: any) {
      console.error('转账失败:', error);
      handleError(error.message || '转账失败');
    } finally {
      setLoading(false);
    }
  }, [publicKey, signTransaction, currentMultisig, connection]);

  const handleTokenSelect = (token: { name: string; mint: string; decimals: number }) => {
    form.setFieldsValue({
      tokenMint: token.mint,
      decimals: token.decimals,
    });
  };

  return (
    <>
      {contextHolder}
      <div>
        <Card>
          <Form form={form} layout="horizontal" onFinish={handleTransfer}>
            {multisigLoading && (
              <Alert
                message="正在加载多签账户信息..."
                type="info"
                showIcon
                icon={<RefreshCw className="w-4 h-4 animate-spin" />}
                style={{ marginBottom: '16px' }}
              />
            )}

            {multisigError && (
              <Alert
                message="加载多签信息失败"
                description={multisigError}
                type="error"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            )}

            {!multisigLoading && !multisigError && !currentMultisig && (
              <Alert
                message="未配置多签账户"
                description="请联系管理员配置多签账户地址"
                type="warning"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            )}

            {currentMultisig && (
              <div style={{ marginBottom: '16px', padding: '12px', border: '1px solid #d9d9d9', borderRadius: '6px', backgroundColor: '#fafafa' }}>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Wallet size={16} />
                    <span style={{ fontWeight: 500 }}>
                      多签账户: {currentMultisig.address.slice(0, 8)}...{currentMultisig.address.slice(-4)}
                    </span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px', fontSize: '12px', color: '#666' }}>
                    <Shield size={12} />
                    <span>{currentMultisig.threshold}/{currentMultisig.members.length}</span>
                  </div>
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  金库余额: {currentMultisig.vault.balanceSOL.toFixed(4)} SOL
                </div>
              </div>
            )}

            <Form.Item label="from" >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space wrap>
                  {commonTokens.map((token) => (
                    <Button
                      key={token.mint}
                      onClick={() => handleTokenSelect(token)}
                      type={form.getFieldValue('tokenMint') === token.mint ? 'primary' : 'default'}
                      size="small"
                    >
                      {token.name}
                    </Button>
                  ))}
                </Space>
              </Space>
            </Form.Item>

            <Form.Item
              name="decimals"
              label="Token小数位数"
              hidden={!form.getFieldValue('tokenMint')}
            >
              <Input type="number" min={0} max={18} />
            </Form.Item>

            <Form.Item
              name="recipient"
              label="to"
              rules={[{ required: true, message: '请输入接收地址' }]}
            >
              <Input placeholder="输入接收地址" />
            </Form.Item>

            <Form.Item
              name="amount"
              label="Amount"
              rules={[{ required: true, message: '请输入转账金额' }]}
            >
              <Input type="number" placeholder="0" />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={!publicKey || !currentMultisig}
                icon={<SendOutlined />}
                block
              >
                创建转账交易
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </>
  );
};

export default SendPage;
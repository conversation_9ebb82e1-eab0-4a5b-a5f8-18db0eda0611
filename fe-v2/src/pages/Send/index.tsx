import React, { useState, useCallback } from 'react';
import { Form, Input, Button, Card, Space, message } from 'antd';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createTransferInstruction } from '@solana/spl-token';
import * as multisig from '@sqds/multisig';
import { SendOutlined } from '@ant-design/icons';
import MultisigSelector from '@/components/MultisigSelector';
import Api from '@/services/api/index';

// 常用Token列表（主网）
const commonTokens = [
  { name: 'USDC', mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', decimals: 6 },
];

const SendPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedMultisig, setSelectedMultisig] = useState('');

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  const handleCreateNew = () => {
    handleError('创建新多签账户功能将在后续步骤中实现');
  };

  const validateInputs = (values: any) => {
    if (!publicKey) {
      throw new Error('请先连接钱包');
    }
    if (!selectedMultisig) {
      throw new Error('请选择多签账户');
    }
    if (!values.recipient) {
      throw new Error('请输入接收地址');
    }
    if (!values.amount || parseFloat(values.amount) <= 0) {
      throw new Error('请输入有效的转账金额');
    }

    // 验证地址格式
    try {
      new PublicKey(selectedMultisig);
    } catch {
      throw new Error('多签地址格式不正确');
    }

    try {
      new PublicKey(values.recipient);
    } catch {
      throw new Error('接收地址格式不正确');
    }

    if (values.tokenMint) {
      try {
        new PublicKey(values.tokenMint);
      } catch {
        throw new Error('Token合约地址格式不正确');
      }
    }
  };

  const handleTransfer = useCallback(async (values: any) => {
    if (!signTransaction) {
      handleError('钱包不支持签名功能');
      return;
    }

    try {
      setLoading(true);
      validateInputs(values);

      const multisigPda = new PublicKey(selectedMultisig);
      const recipientPubkey = new PublicKey(values.recipient);

      // 获取多签和金库信息
      const vaultInfo = await Api.getMultisigs();
      const multisigInfo = vaultInfo.multisigs.find(m => m.address === selectedMultisig);
      if (!multisigInfo) {
        throw new Error('未找到指定的多签账户');
      }

      // 检查当前用户是否是多签成员
      const isMember = multisigInfo.members.some(member => member.key === publicKey!.toBase58());
      if (!isMember) {
        throw new Error('当前钱包不是多签成员');
      }

      const vaultPda = new PublicKey(multisigInfo.vault.address);
      let transferInstruction;
      const transferAmount = values.tokenMint
        ? Math.floor(parseFloat(values.amount) * Math.pow(10, values.decimals || 6))
        : Math.floor(parseFloat(values.amount) * LAMPORTS_PER_SOL);

      if (values.tokenMint) {
        // Token 转账
        const tokenMintPubkey = new PublicKey(values.tokenMint);
        const vaultTokenAccount = await getAssociatedTokenAddress(
          tokenMintPubkey,
          vaultPda,
          true
        );
        const recipientTokenAccount = await getAssociatedTokenAddress(
          tokenMintPubkey,
          recipientPubkey
        );

        // 检查金库Token账户余额
        const tokenAccount = multisigInfo.vault.tokenAccounts.find(
          t => t.mint === values.tokenMint
        );
        if (!tokenAccount || tokenAccount.balance < transferAmount) {
          throw new Error(`Token余额不足。当前余额: ${tokenAccount ? (tokenAccount.balance / Math.pow(10, values.decimals || 6)).toFixed(values.decimals || 6) : 0} Token`);
        }

        transferInstruction = createTransferInstruction(
          vaultTokenAccount,
          recipientTokenAccount,
          vaultPda,
          transferAmount,
          [],
          TOKEN_PROGRAM_ID
        );
      } else {
        // SOL 转账
        if (multisigInfo.vault.balance < transferAmount) {
          throw new Error(`SOL余额不足。当前余额: ${multisigInfo.vault.balanceSOL} SOL`);
        }

        transferInstruction = SystemProgram.transfer({
          fromPubkey: vaultPda,
          toPubkey: recipientPubkey,
          lamports: transferAmount,
        });
      }

      const { blockhash } = await Api.getBlockhash();
      const transferMessage = new TransactionMessage({
        payerKey: vaultPda,
        recentBlockhash: blockhash,
        instructions: [transferInstruction],
      });

      const nextTransactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

      // 创建交易并投票
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: `Transfer ${values.amount} ${values.tokenMint ? 'Token' : 'SOL'} to ${values.recipient.substring(0, 8)}...`,
        programId,
      });

      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        isDraft: false,
        programId,
      });

      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        member: publicKey!,
        programId,
      });

      const combinedMessage = new TransactionMessage({
        payerKey: publicKey!,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message());
      const signedTx = await signTransaction(combinedTx);

      // 发送交易
      const result = values.tokenMint
        ? await Api.createTokenTransfer({
            multisigAddress: selectedMultisig,
            recipientAddress: values.recipient,
            tokenMint: values.tokenMint,
            amount: values.amount,
            decimals: values.decimals || 6,
            creatorPublicKey: publicKey!.toBase58(),
            signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
          })
        : await Api.createSolTransfer({
            multisigAddress: selectedMultisig,
            recipientAddress: values.recipient,
            amount: values.amount,
            creatorPublicKey: publicKey!.toBase58(),
            signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
          });

      messageApi.success('转账交易创建成功！等待其他成员投票...');
      handleSuccess(result.signature);

    } catch (error: any) {
      console.error('转账失败:', error);
      handleError(error.message || '转账失败');
    } finally {
      setLoading(false);
    }
  }, [publicKey, signTransaction, selectedMultisig, connection]);

  const handleTokenSelect = (token: { name: string; mint: string; decimals: number }) => {
    form.setFieldsValue({
      tokenMint: token.mint,
      decimals: token.decimals,
    });
  };

  return (
    <>
      {contextHolder}
      <div>
        <Card title="创建转账交易">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleTransfer}
            initialValues={{
              amount: '0.0001',
              decimals: 6,
            }}
          >
            <MultisigSelector
              selectedMultisig={selectedMultisig}
              onSelect={setSelectedMultisig}
              onCreateNew={handleCreateNew}
            />

            <Form.Item
              label="Token类型"
              extra="如不选择则默认转账 SOL"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space wrap>
                  {commonTokens.map((token) => (
                    <Button
                      key={token.mint}
                      onClick={() => handleTokenSelect(token)}
                      type={form.getFieldValue('tokenMint') === token.mint ? 'primary' : 'default'}
                      size="small"
                    >
                      {token.name}
                    </Button>
                  ))}
                </Space>
                <Form.Item
                  name="tokenMint"
                  noStyle
                >
                  <Input placeholder="或输入Token合约地址" />
                </Form.Item>
              </Space>
            </Form.Item>

            <Form.Item
              name="decimals"
              label="Token小数位数"
              hidden={!form.getFieldValue('tokenMint')}
            >
              <Input type="number" min={0} max={18} />
            </Form.Item>

            <Form.Item
              name="recipient"
              label="接收地址"
              rules={[{ required: true, message: '请输入接收地址' }]}
            >
              <Input placeholder="输入接收地址" />
            </Form.Item>

            <Form.Item
              name="amount"
              label="转账金额"
              rules={[{ required: true, message: '请输入转账金额' }]}
            >
              <Input type="number" min="0.0001" step="0.0001" placeholder="输入转账金额" />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={!publicKey || !selectedMultisig}
                icon={<SendOutlined />}
                block
              >
                创建转账交易
              </Button>
            </Form.Item>
          </Form>

          <Card size="small" title="说明" style={{ marginTop: 16 }}>
            <ul style={{ paddingLeft: 20, margin: 0 }}>
              <li>支持 SOL 和所有 SPL Token 转账</li>
              <li>需要确保金库有足够的余额</li>
              <li>Token 转账时，接收地址必须有对应的 Token 账户</li>
              <li>请确认 Token 合约地址和小数位数正确</li>
              <li>建议先用小额测试</li>
            </ul>
          </Card>
        </Card>
      </div>
    </>
  );
};

export default SendPage;
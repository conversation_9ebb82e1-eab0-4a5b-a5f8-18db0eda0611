import React from 'react';
import { message } from 'antd';
import Transfer from '@/components/Transfer';

const SendPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  return (
    <>
      {contextHolder}
      <div style={{ padding: '24px' }}>
        <Transfer onSuccess={handleSuccess} onError={handleError} />
      </div>
    </>
  );
};

export default SendPage;
const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function comprehensiveTest() {
  console.log('🔍 综合测试：@sqds/multisig + Solana 官方包');
  console.log('=' .repeat(80));
  
  const results = {
    multisigAddress: MULTISIG_ADDRESS,
    exportTime: new Date().toISOString(),
    tests: []
  };

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
    
    const currentIndex = Number(multisigData.transactionIndex.toString());
    console.log('📊 多签基本信息:');
    console.log('  - 当前交易索引:', currentIndex);
    console.log('  - 成员数量:', multisigData.members.length);
    console.log('  - 阈值:', multisigData.threshold);
    
    // 测试最近的几个交易
    const testIndexes = [currentIndex, currentIndex - 1, currentIndex - 2].filter(i => i > 0);
    
    for (const txIndex of testIndexes) {
      console.log(`\n🔍 测试交易 #${txIndex}:`);
      console.log('-'.repeat(50));
      
      const testResult = {
        transactionIndex: txIndex,
        proposalData: null,
        vaultTransactionData: null,
        solanaTransactionData: null,
        transferInfo: null,
        errors: []
      };
      
      // === 方法1: 使用 @sqds/multisig 获取提案信息 ===
      try {
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(txIndex),
          programId: MULTISIG_PROGRAM_ID
        });
        
        console.log(`📍 提案PDA: ${proposalPda.toBase58()}`);
        
        const proposalAccount = await connection.getAccountInfo(proposalPda);
        if (proposalAccount) {
          const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
          
          // 获取创建时间
          let createdAt = null;
          if (proposalData.status && proposalData.status.timestamp) {
            const timestamp = Number(proposalData.status.timestamp.toString());
            createdAt = new Date(timestamp * 1000).toISOString();
          }
          
          // 推断创建者
          let creator = 'Unknown';
          if (proposalData.approved && proposalData.approved.length > 0) {
            creator = proposalData.approved[0].toBase58();
          }
          
          testResult.proposalData = {
            status: proposalData.status.__kind,
            timestamp: proposalData.status.timestamp ? proposalData.status.timestamp.toString() : null,
            createdAt,
            creator,
            approved: proposalData.approved.map(p => p.toBase58()),
            rejected: proposalData.rejected.map(p => p.toBase58())
          };
          
          console.log(`✅ 提案数据: ${proposalData.status.__kind}, 创建时间: ${createdAt}`);
        } else {
          console.log('❌ 提案账户不存在');
        }
      } catch (err) {
        console.log('❌ 获取提案失败:', err.message);
        testResult.errors.push(`Proposal error: ${err.message}`);
      }
      
      // === 方法2: 使用 @sqds/multisig 获取交易账户信息 ===
      try {
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(txIndex),
          programId: MULTISIG_PROGRAM_ID
        });
        
        console.log(`📍 交易PDA: ${transactionPda.toBase58()}`);
        
        const transactionAccount = await connection.getAccountInfo(transactionPda);
        if (transactionAccount) {
          console.log('✅ 交易账户存在！');
          
          const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
          
          testResult.vaultTransactionData = {
            creator: vaultTransactionData.creator.toBase58(),
            index: vaultTransactionData.index.toString(),
            vaultIndex: vaultTransactionData.vaultIndex,
            hasMessage: !!vaultTransactionData.message
          };
          
          console.log(`  - 创建者: ${vaultTransactionData.creator.toBase58()}`);
          console.log(`  - Vault索引: ${vaultTransactionData.vaultIndex}`);
          
          // 🎯 关键：解析交易消息
          if (vaultTransactionData.message) {
            const message = vaultTransactionData.message;
            console.log(`  - 📨 交易消息存在:`);
            console.log(`    - 账户数量: ${message.accountKeys.length}`);
            console.log(`    - 指令数量: ${message.instructions.length}`);
            
            // 分析每个指令
            for (let i = 0; i < message.instructions.length; i++) {
              const instruction = message.instructions[i];
              const programId = message.accountKeys[instruction.programIdIndex];
              
              console.log(`    - 指令 ${i + 1}:`);
              console.log(`      - 程序ID: ${programId.toBase58()}`);
              console.log(`      - 账户索引: [${Array.from(instruction.accountIndexes).join(', ')}]`);
              console.log(`      - 数据长度: ${instruction.data.length} bytes`);
              
              // 解析SOL转账
              if (programId.toBase58() === '11111111111111111111111111111112') {
                console.log(`      - 🎯 系统程序指令（SOL转账）`);
                
                if (instruction.data.length >= 12) {
                  try {
                    const dataView = new DataView(instruction.data.buffer);
                    const instructionType = dataView.getUint32(0, true);
                    
                    if (instructionType === 2) { // Transfer
                      const lamports = dataView.getBigUint64(4, true);
                      const sol = Number(lamports) / **********;
                      
                      const accountIndexes = Array.from(instruction.accountIndexes);
                      const fromAccount = message.accountKeys[accountIndexes[0]];
                      const toAccount = message.accountKeys[accountIndexes[1]];
                      
                      testResult.transferInfo = {
                        type: 'SOL',
                        amount: sol,
                        fromAddress: fromAccount.toBase58(),
                        toAddress: toAccount.toBase58(),
                        source: 'multisig_message'
                      };
                      
                      console.log(`      - 💰 转账金额: ${sol} SOL`);
                      console.log(`      - 📤 从: ${fromAccount.toBase58()}`);
                      console.log(`      - 📥 到: ${toAccount.toBase58()}`);
                    }
                  } catch (parseErr) {
                    console.log(`      - ❌ 解析失败: ${parseErr.message}`);
                  }
                }
              }
              
              // 解析Token转账
              else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                console.log(`      - 🎯 Token程序指令`);
                
                if (instruction.data.length > 0) {
                  const instructionTypeToken = instruction.data[0];
                  
                  if (instructionTypeToken === 3) { // Transfer
                    if (instruction.data.length >= 9) {
                      try {
                        const dataView = new DataView(instruction.data.buffer);
                        const amount = dataView.getBigUint64(1, true);
                        
                        const accountIndexes = Array.from(instruction.accountIndexes);
                        const sourceAccount = message.accountKeys[accountIndexes[0]];
                        const destAccount = message.accountKeys[accountIndexes[1]];
                        
                        testResult.transferInfo = {
                          type: 'TOKEN',
                          amount: Number(amount),
                          fromAddress: sourceAccount.toBase58(),
                          toAddress: destAccount.toBase58(),
                          source: 'multisig_message'
                        };
                        
                        console.log(`      - 🪙 Token转账: ${amount} (原始单位)`);
                        console.log(`      - 📤 从: ${sourceAccount.toBase58()}`);
                        console.log(`      - 📥 到: ${destAccount.toBase58()}`);
                      } catch (parseErr) {
                        console.log(`      - ❌ 解析Token失败: ${parseErr.message}`);
                      }
                    }
                  }
                }
              }
            }
          } else {
            console.log('  - ❌ 交易消息不存在');
          }
        } else {
          console.log('❌ 交易账户不存在');
        }
      } catch (err) {
        console.log('❌ 获取交易账户失败:', err.message);
        testResult.errors.push(`VaultTransaction error: ${err.message}`);
      }
      
      // === 方法3: 使用 Solana 官方包通过签名获取交易信息 ===
      try {
        console.log('🔍 尝试通过 Solana 官方包获取交易信息...');
        
        // 获取提案账户的签名历史
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(txIndex),
          programId: MULTISIG_PROGRAM_ID
        });
        
        const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 5 });
        
        if (signatures.length > 0) {
          console.log(`  - 找到 ${signatures.length} 个签名`);
          
          // 分析每个签名对应的交易
          for (let i = 0; i < Math.min(signatures.length, 2); i++) {
            const sig = signatures[i];
            console.log(`  - 分析签名 ${i + 1}: ${sig.signature.slice(0, 8)}...`);
            
            try {
              const txInfo = await connection.getTransaction(sig.signature, {
                maxSupportedTransactionVersion: 0
              });
              
              if (txInfo && txInfo.transaction) {
                console.log(`    - ✅ 获取到交易信息`);
                console.log(`    - 区块时间: ${new Date(txInfo.blockTime * 1000).toISOString()}`);
                console.log(`    - 费用: ${txInfo.meta?.fee || '未知'}`);
                
                // 分析交易指令
                const message = txInfo.transaction.message;
                if (message && message.instructions) {
                  console.log(`    - 指令数量: ${message.instructions.length}`);
                  
                  message.instructions.forEach((instruction, idx) => {
                    const programId = message.accountKeys[instruction.programIdIndex];
                    console.log(`    - 指令 ${idx + 1}: ${programId.toBase58().slice(0, 8)}...`);
                    
                    // 检查是否是转账指令
                    if (programId.toBase58() === '11111111111111111111111111111112') {
                      console.log(`      - 🎯 系统程序指令`);
                      
                      if (instruction.data && instruction.data.length >= 12) {
                        try {
                          const dataView = new DataView(instruction.data.buffer);
                          const instructionType = dataView.getUint32(0, true);
                          
                          if (instructionType === 2) { // Transfer
                            const lamports = dataView.getBigUint64(4, true);
                            const sol = Number(lamports) / **********;
                            
                            const fromAccount = message.accountKeys[instruction.accounts[0]];
                            const toAccount = message.accountKeys[instruction.accounts[1]];
                            
                            if (!testResult.transferInfo) {
                              testResult.transferInfo = {
                                type: 'SOL',
                                amount: sol,
                                fromAddress: fromAccount.toBase58(),
                                toAddress: toAccount.toBase58(),
                                source: 'solana_transaction'
                              };
                            }
                            
                            console.log(`      - 💰 SOL转账: ${sol} SOL`);
                            console.log(`      - 📥 到: ${toAccount.toBase58().slice(0, 8)}...`);
                          }
                        } catch (parseErr) {
                          console.log(`      - ❌ 解析失败: ${parseErr.message}`);
                        }
                      }
                    }
                  });
                }
                
                testResult.solanaTransactionData = {
                  signature: sig.signature,
                  blockTime: txInfo.blockTime,
                  fee: txInfo.meta?.fee || 0,
                  success: !txInfo.meta?.err,
                  instructionCount: message?.instructions?.length || 0
                };
                
              } else {
                console.log(`    - ❌ 无法获取交易详情`);
              }
            } catch (txErr) {
              console.log(`    - ❌ 获取交易失败: ${txErr.message}`);
            }
            
            // 添加延迟避免API限制
            if (i < Math.min(signatures.length, 2) - 1) {
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        } else {
          console.log('  - ❌ 没有找到签名');
        }
      } catch (err) {
        console.log('❌ Solana官方包方法失败:', err.message);
        testResult.errors.push(`Solana method error: ${err.message}`);
      }
      
      results.tests.push(testResult);
      
      // 添加延迟避免API限制
      if (txIndex !== testIndexes[testIndexes.length - 1]) {
        console.log('⏳ 等待2秒...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
  } catch (error) {
    console.error('❌ 综合测试失败:', error.message);
    results.error = error.message;
  }
  
  // 保存结果
  fs.writeFileSync('comprehensive-test-results.json', JSON.stringify(results, null, 2));
  
  // 显示总结
  console.log('\n📊 综合测试总结:');
  console.log('=' .repeat(80));
  
  const hasProposalData = results.tests.some(t => t.proposalData);
  const hasVaultTransactionData = results.tests.some(t => t.vaultTransactionData);
  const hasTransferInfo = results.tests.some(t => t.transferInfo);
  const hasSolanaTransactionData = results.tests.some(t => t.solanaTransactionData);
  
  console.log(`✅ 提案数据获取: ${hasProposalData ? '成功' : '失败'}`);
  console.log(`✅ 交易账户数据: ${hasVaultTransactionData ? '成功' : '失败'}`);
  console.log(`✅ 转账信息解析: ${hasTransferInfo ? '成功' : '失败'}`);
  console.log(`✅ Solana交易数据: ${hasSolanaTransactionData ? '成功' : '失败'}`);
  
  if (hasTransferInfo) {
    console.log('\n🎉 找到转账信息的交易:');
    results.tests.forEach(test => {
      if (test.transferInfo) {
        console.log(`  - 交易 #${test.transactionIndex}: ${test.transferInfo.amount} ${test.transferInfo.type} → ${test.transferInfo.toAddress.slice(0, 8)}... (来源: ${test.transferInfo.source})`);
      }
    });
  }
  
  console.log(`\n📄 详细结果已保存到: comprehensive-test-results.json`);
}

comprehensiveTest().then(() => {
  console.log('\n🎉 综合测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置 - 尝试一些知名的多签地址
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const TEST_ADDRESSES = [
  "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr", // 原地址
  "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM", // 测试地址1
  "DRpbCBMxVnDK7maPM5tGv6MvB3v1sRMC7Probn7KMqg",  // 测试地址2
];

const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function testDifferentMultisigs() {
  console.log('🔍 测试不同的多签地址寻找包含交易数据的地址');
  console.log('=' .repeat(80));
  
  const results = {
    exportTime: new Date().toISOString(),
    addressTests: []
  };

  for (const address of TEST_ADDRESSES) {
    console.log(`\n🔍 测试多签地址: ${address}`);
    console.log('-'.repeat(60));
    
    const addressResult = {
      address,
      isValidMultisig: false,
      multisigInfo: null,
      hasTransactionData: false,
      sampleTransaction: null,
      error: null
    };
    
    try {
      const multisigPubkey = new PublicKey(address);
      
      // 检查是否是有效的多签账户
      try {
        const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
        
        addressResult.isValidMultisig = true;
        addressResult.multisigInfo = {
          threshold: multisigData.threshold,
          memberCount: multisigData.members.length,
          transactionIndex: multisigData.transactionIndex.toString(),
          createKey: multisigData.createKey.toBase58()
        };
        
        console.log('✅ 有效的多签账户');
        console.log(`  - 阈值: ${multisigData.threshold}`);
        console.log(`  - 成员数: ${multisigData.members.length}`);
        console.log(`  - 交易索引: ${multisigData.transactionIndex.toString()}`);
        
        // 测试最新的交易是否有数据
        const currentIndex = Number(multisigData.transactionIndex.toString());
        if (currentIndex > 0) {
          console.log(`\n🔍 测试最新交易 #${currentIndex}...`);
          
          const [transactionPda] = multisig.getTransactionPda({
            multisigPda: multisigPubkey,
            transactionIndex: BigInt(currentIndex),
            programId: MULTISIG_PROGRAM_ID
          });
          
          const transactionAccount = await connection.getAccountInfo(transactionPda);
          if (transactionAccount) {
            console.log('🎉 找到交易账户数据！');
            addressResult.hasTransactionData = true;
            
            try {
              const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
              
              addressResult.sampleTransaction = {
                creator: vaultTransactionData.creator.toBase58(),
                index: vaultTransactionData.index.toString(),
                vaultIndex: vaultTransactionData.vaultIndex,
                hasMessage: !!vaultTransactionData.message,
                messageDetails: null
              };
              
              console.log(`  - 创建者: ${vaultTransactionData.creator.toBase58()}`);
              console.log(`  - 有消息: ${!!vaultTransactionData.message}`);
              
              if (vaultTransactionData.message) {
                const message = vaultTransactionData.message;
                console.log(`  - 账户数: ${message.accountKeys.length}`);
                console.log(`  - 指令数: ${message.instructions.length}`);
                
                addressResult.sampleTransaction.messageDetails = {
                  accountCount: message.accountKeys.length,
                  instructionCount: message.instructions.length,
                  accounts: message.accountKeys.map(key => key.toBase58()),
                  instructions: message.instructions.map(inst => ({
                    programIdIndex: inst.programIdIndex,
                    programId: message.accountKeys[inst.programIdIndex].toBase58(),
                    accountIndexes: Array.from(inst.accountIndexes),
                    dataLength: inst.data.length
                  }))
                };
                
                // 分析转账信息
                for (const instruction of message.instructions) {
                  const programId = message.accountKeys[instruction.programIdIndex];
                  
                  if (programId.toBase58() === '11111111111111111111111111111112') {
                    console.log(`  - 🎯 发现SOL转账指令！`);
                    
                    if (instruction.data.length >= 12) {
                      try {
                        const dataView = new DataView(instruction.data.buffer);
                        const instructionType = dataView.getUint32(0, true);
                        
                        if (instructionType === 2) {
                          const lamports = dataView.getBigUint64(4, true);
                          const sol = Number(lamports) / **********;
                          
                          const accountIndexes = Array.from(instruction.accountIndexes);
                          const fromAccount = message.accountKeys[accountIndexes[0]];
                          const toAccount = message.accountKeys[accountIndexes[1]];
                          
                          console.log(`    - 💰 金额: ${sol} SOL`);
                          console.log(`    - 📤 从: ${fromAccount.toBase58()}`);
                          console.log(`    - 📥 到: ${toAccount.toBase58()}`);
                          
                          addressResult.sampleTransaction.transferInfo = {
                            type: 'SOL',
                            amount: sol,
                            fromAddress: fromAccount.toBase58(),
                            toAddress: toAccount.toBase58()
                          };
                        }
                      } catch (parseErr) {
                        console.log(`    - ❌ 解析失败: ${parseErr.message}`);
                      }
                    }
                  } else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                    console.log(`  - 🎯 发现Token转账指令！`);
                    
                    if (instruction.data.length > 0 && instruction.data[0] === 3) {
                      if (instruction.data.length >= 9) {
                        try {
                          const dataView = new DataView(instruction.data.buffer);
                          const amount = dataView.getBigUint64(1, true);
                          
                          const accountIndexes = Array.from(instruction.accountIndexes);
                          const sourceAccount = message.accountKeys[accountIndexes[0]];
                          const destAccount = message.accountKeys[accountIndexes[1]];
                          
                          console.log(`    - 🪙 金额: ${amount} (原始单位)`);
                          console.log(`    - 📤 从: ${sourceAccount.toBase58()}`);
                          console.log(`    - 📥 到: ${destAccount.toBase58()}`);
                          
                          addressResult.sampleTransaction.transferInfo = {
                            type: 'TOKEN',
                            amount: Number(amount),
                            fromAddress: sourceAccount.toBase58(),
                            toAddress: destAccount.toBase58()
                          };
                        } catch (parseErr) {
                          console.log(`    - ❌ 解析Token失败: ${parseErr.message}`);
                        }
                      }
                    }
                  }
                }
              }
            } catch (parseErr) {
              console.log(`❌ 解析交易数据失败: ${parseErr.message}`);
              addressResult.error = `Parse transaction failed: ${parseErr.message}`;
            }
          } else {
            console.log('❌ 交易账户不存在');
          }
        } else {
          console.log('❌ 没有交易记录');
        }
        
      } catch (multisigErr) {
        console.log('❌ 不是有效的多签账户或无法访问');
        addressResult.error = `Invalid multisig: ${multisigErr.message}`;
      }
      
    } catch (err) {
      console.log(`❌ 测试地址失败: ${err.message}`);
      addressResult.error = err.message;
    }
    
    results.addressTests.push(addressResult);
    
    // 添加延迟避免API限制
    if (address !== TEST_ADDRESSES[TEST_ADDRESSES.length - 1]) {
      console.log('⏳ 等待3秒...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // 保存结果
  fs.writeFileSync('different-multisig-test-results.json', JSON.stringify(results, null, 2));
  
  // 显示总结
  console.log('\n📊 测试总结:');
  console.log('=' .repeat(80));
  
  const validMultisigs = results.addressTests.filter(t => t.isValidMultisig);
  const withTransactionData = results.addressTests.filter(t => t.hasTransactionData);
  const withTransferInfo = results.addressTests.filter(t => t.sampleTransaction?.transferInfo);
  
  console.log(`📋 测试地址数: ${TEST_ADDRESSES.length}`);
  console.log(`✅ 有效多签账户: ${validMultisigs.length}`);
  console.log(`📦 有交易数据: ${withTransactionData.length}`);
  console.log(`💰 有转账信息: ${withTransferInfo.length}`);
  
  if (withTransferInfo.length > 0) {
    console.log('\n🎉 找到包含转账信息的多签地址:');
    withTransferInfo.forEach(test => {
      const transfer = test.sampleTransaction.transferInfo;
      console.log(`  - ${test.address}: ${transfer.amount} ${transfer.type} → ${transfer.toAddress.slice(0, 8)}...`);
    });
  } else {
    console.log('\n😞 没有找到包含转账信息的多签地址');
    console.log('💡 建议:');
    console.log('  1. 尝试更多的多签地址');
    console.log('  2. 查找最近创建的多签账户');
    console.log('  3. 使用 Squads 官方示例地址');
  }
  
  console.log(`\n📄 详细结果已保存到: different-multisig-test-results.json`);
}

testDifferentMultisigs().then(() => {
  console.log('\n🎉 不同多签地址测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

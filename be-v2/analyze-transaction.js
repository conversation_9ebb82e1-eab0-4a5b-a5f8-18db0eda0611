const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function analyzeTransaction() {
  console.log('🔍 分析多签交易数据结构');
  console.log('=' .repeat(60));

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    const currentTransactionIndex = Number(multisigData.transactionIndex.toString());
    console.log('📊 当前交易索引:', currentTransactionIndex);

    // 分析最近的交易
    const i = currentTransactionIndex;

    // 获取提案PDA
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(i),
      programId: MULTISIG_PROGRAM_ID
    });

    console.log('📍 提案PDA:', proposalPda.toBase58());

    // 获取提案账户信息
    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (!proposalAccount) {
      console.log('❌ 提案账户不存在');
      return;
    }

    const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
    console.log('✅ 提案数据:');
    console.log('  - 创建者:', proposalData.creator ? proposalData.creator.toBase58() : '未知');
    console.log('  - 状态:', proposalData.status.__kind);
    console.log('  - 批准者:', proposalData.approved.map(p => p.toBase58()));
    console.log('  - 拒绝者:', proposalData.rejected.map(p => p.toBase58()));

    // 获取交易签名历史
    const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 10 });
    console.log('\n📅 交易签名历史:');

    for (let j = 0; j < Math.min(signatures.length, 3); j++) {
      const sig = signatures[j];
      console.log(`\n🔍 分析签名 ${j + 1}: ${sig.signature}`);
      console.log('  - 时间:', new Date(sig.blockTime * 1000).toISOString());

      try {
        const txInfo = await connection.getTransaction(sig.signature, {
          maxSupportedTransactionVersion: 0
        });

        if (txInfo && txInfo.transaction) {
          console.log('  - ✅ 交易信息获取成功');
          console.log('  - 费用:', txInfo.meta?.fee || '未知');
          console.log('  - 状态:', txInfo.meta?.err ? '失败' : '成功');

          // 分析交易消息
          console.log('  - 交易结构:', Object.keys(txInfo.transaction));

          let message;
          if (txInfo.transaction.message) {
            message = txInfo.transaction.message;
          } else if (txInfo.transaction.transaction && txInfo.transaction.transaction.message) {
            message = txInfo.transaction.transaction.message;
          } else {
            console.log('  - ❌ 无法找到交易消息结构');
            console.log('  - 交易对象:', JSON.stringify(txInfo.transaction, null, 2).slice(0, 500));
            continue;
          }

          console.log('  - 消息结构:', Object.keys(message));

          if (!message.accountKeys || !message.instructions) {
            console.log('  - ❌ 消息结构不完整');
            console.log('  - accountKeys存在:', !!message.accountKeys);
            console.log('  - instructions存在:', !!message.instructions);
            continue;
          }

          console.log('  - 账户数量:', message.accountKeys.length);
          console.log('  - 指令数量:', message.instructions.length);

          // 显示所有账户
          console.log('  - 📋 涉及的账户:');
          message.accountKeys.forEach((key, idx) => {
            const keyStr = key.toBase58();
            let description = '';

            if (keyStr === MULTISIG_ADDRESS) {
              description = ' (多签账户)';
            } else if (keyStr === MULTISIG_PROGRAM_ID.toBase58()) {
              description = ' (Squads程序)';
            } else if (keyStr === '11111111111111111111111111111112') {
              description = ' (系统程序)';
            } else if (keyStr === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
              description = ' (Token程序)';
            } else if (multisigData.members.some(m => m.key.toBase58() === keyStr)) {
              description = ' (多签成员)';
            }

            console.log(`    ${idx}: ${keyStr.slice(0, 8)}...${keyStr.slice(-4)}${description}`);
          });

          // 分析每个指令
          console.log('  - 🔧 指令分析:');
          message.instructions.forEach((instruction, idx) => {
            const programId = message.accountKeys[instruction.programIdIndex];
            console.log(`    指令 ${idx + 1}:`);
            console.log(`      - 程序: ${programId.toBase58().slice(0, 8)}...`);
            console.log(`      - 账户索引: [${instruction.accounts.join(', ')}]`);
            console.log(`      - 数据长度: ${instruction.data.length} bytes`);

            // 如果是系统程序，尝试解析转账
            if (programId.toBase58() === '11111111111111111111111111111112') {
              console.log('      - 🎯 系统程序指令 (可能是SOL转账)');

              if (instruction.data.length >= 12) {
                try {
                  const dataView = new DataView(instruction.data.buffer);
                  const instructionType = dataView.getUint32(0, true);

                  if (instructionType === 2) { // Transfer instruction
                    const lamports = dataView.getBigUint64(4, true);
                    const sol = Number(lamports) / **********;
                    console.log(`      - 💰 转账金额: ${sol} SOL`);

                    if (instruction.accounts.length >= 2) {
                      const fromIdx = instruction.accounts[0];
                      const toIdx = instruction.accounts[1];
                      console.log(`      - 📤 从: ${message.accountKeys[fromIdx].toBase58().slice(0, 8)}...`);
                      console.log(`      - 📥 到: ${message.accountKeys[toIdx].toBase58().slice(0, 8)}...`);
                    }
                  }
                } catch (parseErr) {
                  console.log('      - ❌ 解析转账数据失败:', parseErr.message);
                }
              }
            }

            // 如果是Token程序
            else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
              console.log('      - 🎯 Token程序指令');
              // Token指令解析会更复杂，这里先标记
            }

            // 如果是Squads程序
            else if (programId.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
              console.log('      - 🎯 Squads多签程序指令');
            }
          });

        } else {
          console.log('  - ❌ 无法获取交易详情');
        }

      } catch (err) {
        console.log('  - ❌ 分析交易失败:', err.message);
      }

      // 添加延迟避免API限制
      if (j < Math.min(signatures.length, 3) - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
  }
}

// 运行分析
analyzeTransaction().then(() => {
  console.log('\n✅ 分析完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 分析异常:', err);
  process.exit(1);
});

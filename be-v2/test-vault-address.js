const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const VAULT_ADDRESS = "EsvC81UWiKC3DPuqZwKYqYavqEg15U7ZUKLhrgNaa5Br"; // 金库地址
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function testVaultAddress() {
  console.log('🔍 测试金库地址获取交易记录');
  console.log(`📍 多签地址: ${MULTISIG_ADDRESS}`);
  console.log(`🏦 金库地址: ${VAULT_ADDRESS}`);
  console.log('=' .repeat(80));
  
  const results = {
    multisigAddress: MULTISIG_ADDRESS,
    vaultAddress: VAULT_ADDRESS,
    exportTime: new Date().toISOString(),
    findings: []
  };

  try {
    // 1. 验证多签和金库的关系
    console.log('\n🔍 步骤1: 验证多签和金库的关系');
    console.log('-'.repeat(50));
    
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const vaultPubkey = new PublicKey(VAULT_ADDRESS);
    
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
    console.log('✅ 多签数据获取成功');
    console.log(`  - 当前交易索引: ${multisigData.transactionIndex.toString()}`);
    console.log(`  - 成员数量: ${multisigData.members.length}`);
    
    // 验证金库地址是否正确
    for (let vaultIndex = 0; vaultIndex < 5; vaultIndex++) {
      const [calculatedVaultPda] = multisig.getVaultPda({
        multisigPda: multisigPubkey,
        index: vaultIndex,
        programId: MULTISIG_PROGRAM_ID
      });
      
      if (calculatedVaultPda.toBase58() === VAULT_ADDRESS) {
        console.log(`✅ 金库地址验证成功！这是 Vault #${vaultIndex}`);
        results.findings.push({
          type: 'vault_verification',
          success: true,
          vaultIndex,
          calculatedAddress: calculatedVaultPda.toBase58()
        });
        break;
      }
    }
    
    // 2. 获取金库地址的交易历史
    console.log('\n🔍 步骤2: 获取金库地址的交易历史');
    console.log('-'.repeat(50));
    
    const vaultSignatures = await connection.getSignaturesForAddress(vaultPubkey, { limit: 20 });
    console.log(`📅 找到 ${vaultSignatures.length} 个金库交易签名`);
    
    if (vaultSignatures.length > 0) {
      results.findings.push({
        type: 'vault_signatures',
        count: vaultSignatures.length,
        signatures: vaultSignatures.map(sig => ({
          signature: sig.signature,
          blockTime: sig.blockTime,
          timeISO: new Date(sig.blockTime * 1000).toISOString(),
          slot: sig.slot,
          err: sig.err
        }))
      });
      
      // 3. 分析每个交易寻找转账信息
      console.log('\n🔍 步骤3: 分析交易寻找转账信息');
      console.log('-'.repeat(50));
      
      const transferTransactions = [];
      
      for (let i = 0; i < Math.min(vaultSignatures.length, 10); i++) {
        const sig = vaultSignatures[i];
        console.log(`\n📋 分析交易 ${i + 1}: ${sig.signature.slice(0, 8)}...`);
        console.log(`  - 时间: ${new Date(sig.blockTime * 1000).toISOString()}`);
        
        try {
          const txInfo = await connection.getTransaction(sig.signature, {
            maxSupportedTransactionVersion: 0
          });
          
          if (txInfo && txInfo.transaction && txInfo.transaction.message) {
            const message = txInfo.transaction.message;
            console.log(`  - ✅ 获取到交易，指令数: ${message.instructions.length}`);
            console.log(`  - 费用: ${txInfo.meta?.fee || '未知'}`);
            console.log(`  - 状态: ${txInfo.meta?.err ? '失败' : '成功'}`);
            
            // 显示涉及的账户
            console.log(`  - 📋 涉及的账户 (${message.accountKeys.length}个):`);
            message.accountKeys.slice(0, 5).forEach((key, idx) => {
              let description = '';
              if (key.toBase58() === VAULT_ADDRESS) {
                description = ' (金库地址)';
              } else if (key.toBase58() === MULTISIG_ADDRESS) {
                description = ' (多签地址)';
              } else if (key.toBase58() === '11111111111111111111111111111112') {
                description = ' (系统程序)';
              } else if (key.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                description = ' (Token程序)';
              } else if (key.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
                description = ' (Squads程序)';
              }
              console.log(`    ${idx}: ${key.toBase58().slice(0, 8)}...${key.toBase58().slice(-4)}${description}`);
            });
            
            if (message.accountKeys.length > 5) {
              console.log(`    ... 还有 ${message.accountKeys.length - 5} 个账户`);
            }
            
            // 分析每个指令
            console.log(`  - 🔧 指令分析:`);
            message.instructions.forEach((instruction, idx) => {
              const programId = message.accountKeys[instruction.programIdIndex];
              console.log(`    指令 ${idx + 1}:`);
              console.log(`      - 程序: ${programId.toBase58().slice(0, 8)}...`);
              console.log(`      - 账户索引: [${instruction.accounts.join(', ')}]`);
              console.log(`      - 数据长度: ${instruction.data.length} bytes`);
              
              // 🎯 重点：分析转账指令
              if (programId.toBase58() === '11111111111111111111111111111112') {
                console.log(`      - 🎯 系统程序指令（SOL转账）`);
                
                if (instruction.data && instruction.data.length >= 12) {
                  try {
                    const dataView = new DataView(instruction.data.buffer);
                    const instructionType = dataView.getUint32(0, true);
                    
                    if (instructionType === 2) { // Transfer
                      const lamports = dataView.getBigUint64(4, true);
                      const sol = Number(lamports) / **********;
                      
                      const fromAccount = message.accountKeys[instruction.accounts[0]];
                      const toAccount = message.accountKeys[instruction.accounts[1]];
                      
                      console.log(`      - 💰 SOL转账发现！`);
                      console.log(`        - 金额: ${sol} SOL`);
                      console.log(`        - 从: ${fromAccount.toBase58()}`);
                      console.log(`        - 到: ${toAccount.toBase58()}`);
                      
                      const transferInfo = {
                        signature: sig.signature,
                        blockTime: sig.blockTime,
                        timeISO: new Date(sig.blockTime * 1000).toISOString(),
                        type: 'SOL',
                        amount: sol,
                        fromAddress: fromAccount.toBase58(),
                        toAddress: toAccount.toBase58(),
                        instructionIndex: idx,
                        fee: txInfo.meta?.fee || 0
                      };
                      
                      transferTransactions.push(transferInfo);
                      
                      // 检查是否是从金库发出的转账
                      if (fromAccount.toBase58() === VAULT_ADDRESS) {
                        console.log(`        - 🎉 这是从金库发出的转账！`);
                      }
                    } else {
                      console.log(`      - 指令类型: ${instructionType} (不是转账)`);
                    }
                  } catch (parseErr) {
                    console.log(`      - ❌ 解析SOL转账失败: ${parseErr.message}`);
                  }
                }
              }
              
              // Token转账分析
              else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                console.log(`      - 🎯 Token程序指令`);
                
                if (instruction.data && instruction.data.length > 0) {
                  const instructionTypeToken = instruction.data[0];
                  console.log(`      - Token指令类型: ${instructionTypeToken}`);
                  
                  if (instructionTypeToken === 3) { // Transfer
                    if (instruction.data.length >= 9) {
                      try {
                        const dataView = new DataView(instruction.data.buffer);
                        const amount = dataView.getBigUint64(1, true);
                        
                        const sourceAccount = message.accountKeys[instruction.accounts[0]];
                        const destAccount = message.accountKeys[instruction.accounts[1]];
                        const authority = message.accountKeys[instruction.accounts[2]];
                        
                        console.log(`      - 🪙 Token转账发现！`);
                        console.log(`        - 金额: ${amount} (原始单位)`);
                        console.log(`        - 从: ${sourceAccount.toBase58()}`);
                        console.log(`        - 到: ${destAccount.toBase58()}`);
                        console.log(`        - 授权: ${authority.toBase58()}`);
                        
                        const transferInfo = {
                          signature: sig.signature,
                          blockTime: sig.blockTime,
                          timeISO: new Date(sig.blockTime * 1000).toISOString(),
                          type: 'TOKEN',
                          amount: Number(amount),
                          fromAddress: sourceAccount.toBase58(),
                          toAddress: destAccount.toBase58(),
                          authority: authority.toBase58(),
                          instructionIndex: idx,
                          fee: txInfo.meta?.fee || 0
                        };
                        
                        transferTransactions.push(transferInfo);
                      } catch (parseErr) {
                        console.log(`      - ❌ 解析Token转账失败: ${parseErr.message}`);
                      }
                    }
                  }
                }
              }
              
              // Squads程序指令
              else if (programId.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
                console.log(`      - 🎯 Squads多签程序指令`);
              }
              
              // 其他程序
              else {
                console.log(`      - 🔍 其他程序: ${programId.toBase58()}`);
              }
            });
            
          } else {
            console.log(`  - ❌ 无法获取交易详情`);
          }
          
        } catch (txErr) {
          console.log(`  - ❌ 获取交易失败: ${txErr.message}`);
        }
        
        // 添加延迟避免API限制
        if (i < Math.min(vaultSignatures.length, 10) - 1) {
          console.log('  ⏳ 等待1秒...');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      if (transferTransactions.length > 0) {
        results.findings.push({
          type: 'transfer_transactions',
          count: transferTransactions.length,
          transfers: transferTransactions
        });
      }
      
    } else {
      console.log('❌ 金库地址没有交易历史');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    results.error = error.message;
  }
  
  // 保存结果
  fs.writeFileSync('vault-address-test-results.json', JSON.stringify(results, null, 2));
  
  // 显示总结
  console.log('\n📊 金库地址测试总结:');
  console.log('=' .repeat(80));
  
  const vaultSignatures = results.findings.find(f => f.type === 'vault_signatures');
  const transferTransactions = results.findings.find(f => f.type === 'transfer_transactions');
  
  console.log(`🏦 金库地址: ${VAULT_ADDRESS}`);
  console.log(`📅 交易签名数: ${vaultSignatures ? vaultSignatures.count : 0}`);
  console.log(`💰 转账交易数: ${transferTransactions ? transferTransactions.count : 0}`);
  
  if (transferTransactions && transferTransactions.count > 0) {
    console.log('\n🎉 找到的转账信息:');
    transferTransactions.transfers.forEach((transfer, idx) => {
      console.log(`  ${idx + 1}. ${transfer.timeISO}`);
      console.log(`     💰 ${transfer.amount} ${transfer.type} → ${transfer.toAddress.slice(0, 8)}...`);
      console.log(`     🔗 ${transfer.signature.slice(0, 8)}...`);
    });
    
    console.log('\n✅ 成功！我们可以从金库地址获取转账信息！');
  } else {
    console.log('\n😞 没有找到转账信息');
  }
  
  console.log(`\n📄 详细结果已保存到: vault-address-test-results.json`);
}

testVaultAddress().then(() => {
  console.log('\n🎉 金库地址测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function testMultisigData() {
  console.log('🔍 测试多签地址:', MULTISIG_ADDRESS);
  console.log('=' .repeat(80));

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    console.log('📊 多签基本信息:');
    console.log('  - 阈值:', multisigData.threshold);
    console.log('  - 成员数量:', multisigData.members.length);
    console.log('  - 当前交易索引:', multisigData.transactionIndex.toString());
    console.log('  - 创建者:', multisigData.createKey.toBase58());
    console.log();

    // 测试最近的一个交易（避免API限制）
    const currentTransactionIndex = Number(multisigData.transactionIndex.toString());
    const testIndexes = [currentTransactionIndex];

    for (const i of testIndexes) {
      if (i <= 0) continue;

      console.log(`🔍 测试交易 #${i}:`);
      console.log('-'.repeat(40));

      try {
        // 获取提案信息
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const proposalAccount = await connection.getAccountInfo(proposalPda);
        if (!proposalAccount) {
          console.log('  ❌ 提案账户不存在');
          continue;
        }

        const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
        console.log('  ✅ 提案信息:');
        console.log('    - 创建者:', proposalData.creator ? proposalData.creator.toBase58() : '未知');
        console.log('    - 状态:', proposalData.status.__kind);
        console.log('    - 批准数:', proposalData.approved.length);
        console.log('    - 拒绝数:', proposalData.rejected.length);

        // 获取交易详情
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const transactionAccount = await connection.getAccountInfo(transactionPda);
        if (transactionAccount) {
          console.log('  ✅ 交易账户存在');

          try {
            const transactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
            console.log('    - Memo:', transactionData.memo || '(空)');
            console.log('    - 创建者:', transactionData.creator ? transactionData.creator.toBase58() : '未知');

            // 尝试解析交易消息
            if (transactionData.message) {
              console.log('    - 交易消息存在，长度:', transactionData.message.length);

              // 尝试解析指令
              try {
                const message = transactionData.message;
                console.log('    - 消息详情:');
                console.log('      - 账户数量:', message.accountKeys ? message.accountKeys.length : '未知');
                console.log('      - 指令数量:', message.instructions ? message.instructions.length : '未知');

                if (message.instructions && message.instructions.length > 0) {
                  const instruction = message.instructions[0];
                  console.log('      - 第一个指令:');
                  console.log('        - 程序ID索引:', instruction.programIdIndex);
                  console.log('        - 账户索引:', instruction.accounts);
                  console.log('        - 数据长度:', instruction.data ? instruction.data.length : 0);

                  // 检查是否是转账指令
                  if (message.accountKeys && instruction.accounts) {
                    const programIdIndex = instruction.programIdIndex;
                    if (programIdIndex < message.accountKeys.length) {
                      const programId = message.accountKeys[programIdIndex];
                      console.log('        - 程序ID:', programId.toBase58());

                      // 检查是否是系统程序（SOL转账）
                      if (programId.toBase58() === '11111111111111111111111111111112') {
                        console.log('        - 🎯 这是SOL转账指令!');

                        // 解析转账数据
                        if (instruction.data && instruction.data.length >= 12) {
                          const dataView = new DataView(instruction.data.buffer);
                          const instructionType = dataView.getUint32(0, true);
                          if (instructionType === 2) { // Transfer instruction
                            const lamports = dataView.getBigUint64(4, true);
                            const sol = Number(lamports) / **********;
                            console.log('        - 💰 转账金额:', sol, 'SOL');

                            // 获取目标地址
                            if (instruction.accounts.length >= 2) {
                              const toAccountIndex = instruction.accounts[1];
                              if (toAccountIndex < message.accountKeys.length) {
                                const toAddress = message.accountKeys[toAccountIndex];
                                console.log('        - 📍 目标地址:', toAddress.toBase58());
                              }
                            }
                          }
                        }
                      }

                      // 检查是否是Token程序
                      else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                        console.log('        - 🎯 这是Token转账指令!');
                        // Token转账解析会更复杂，需要解析具体的指令数据
                      }
                    }
                  }
                }
              } catch (parseErr) {
                console.log('    - ❌ 解析交易消息失败:', parseErr.message);
              }
            }

          } catch (err) {
            console.log('    - ❌ 解析交易数据失败:', err.message);
          }
        } else {
          console.log('  ❌ 交易账户不存在');
        }

        // 尝试获取账户创建时间（通过区块链历史）
        try {
          const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 5 });
          if (signatures.length > 0) {
            console.log('    - 📅 签名历史:');
            signatures.forEach((sig, index) => {
              const time = sig.blockTime ? new Date(sig.blockTime * 1000).toISOString() : '未知';
              console.log(`      ${index + 1}. ${time} - ${sig.signature.slice(0, 8)}...`);
            });

            // 获取最早的签名作为创建时间
            const oldestSig = signatures[signatures.length - 1];
            if (oldestSig.blockTime) {
              console.log('    - 🎯 推断创建时间:', new Date(oldestSig.blockTime * 1000).toISOString());
            }
          }
        } catch (timeErr) {
          console.log('    - ❌ 获取时间信息失败:', timeErr.message);
        }

        // 尝试通过交易签名获取详细信息
        try {
          const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 1 });
          if (signatures.length > 0) {
            const txSignature = signatures[0].signature;
            console.log('    - 🔍 尝试获取交易详情...');

            const txInfo = await connection.getTransaction(txSignature, {
              maxSupportedTransactionVersion: 0
            });

            if (txInfo) {
              console.log('    - ✅ 交易详情获取成功:');
              console.log('      - 区块时间:', new Date(txInfo.blockTime * 1000).toISOString());
              console.log('      - 费用:', txInfo.meta?.fee || '未知');
              console.log('      - 状态:', txInfo.meta?.err ? '失败' : '成功');

              // 分析交易指令
              if (txInfo.transaction && txInfo.transaction.message) {
                const message = txInfo.transaction.message;
                console.log('      - 指令数量:', message.instructions.length);

                message.instructions.forEach((instruction, idx) => {
                  const programId = message.accountKeys[instruction.programIdIndex];
                  console.log(`      - 指令 ${idx + 1}: ${programId.toBase58().slice(0, 8)}...`);

                  // 检查是否是转账相关指令
                  if (programId.toBase58() === '11111111111111111111111111111112') {
                    console.log('        → 系统程序指令 (可能是SOL转账)');
                  } else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                    console.log('        → Token程序指令 (可能是Token转账)');
                  } else if (programId.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
                    console.log('        → Squads多签程序指令');
                  }
                });
              }
            }
          }
        } catch (txErr) {
          console.log('    - ❌ 获取交易详情失败:', txErr.message);
        }

      } catch (err) {
        console.log('  ❌ 处理交易失败:', err.message);
      }

      console.log();
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testMultisigData().then(() => {
  console.log('✅ 测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

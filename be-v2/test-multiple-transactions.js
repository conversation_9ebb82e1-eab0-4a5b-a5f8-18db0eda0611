const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function testMultipleTransactions() {
  console.log('🔍 测试多个交易索引...');
  
  const results = {
    multisigAddress: MULTISIG_ADDRESS,
    exportTime: new Date().toISOString(),
    summary: {
      totalTested: 0,
      proposalExists: 0,
      transactionExists: 0,
      hasTransferData: 0
    },
    transactions: []
  };

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
    
    const currentIndex = Number(multisigData.transactionIndex.toString());
    console.log('当前交易索引:', currentIndex);
    
    // 测试最近10个交易
    const testIndexes = [];
    for (let i = Math.max(1, currentIndex - 9); i <= currentIndex; i++) {
      testIndexes.push(i);
    }
    
    console.log('将测试交易索引:', testIndexes);
    
    for (const txIndex of testIndexes) {
      console.log(`\n🔍 测试交易 #${txIndex}...`);
      results.summary.totalTested++;
      
      const txResult = {
        transactionIndex: txIndex,
        proposalExists: false,
        transactionExists: false,
        hasTransferData: false,
        creator: null,
        createdAt: null,
        transferInfo: null
      };
      
      // 检查提案
      const [proposalPda] = multisig.getProposalPda({
        multisigPda: multisigPubkey,
        transactionIndex: BigInt(txIndex),
        programId: MULTISIG_PROGRAM_ID
      });
      
      const proposalAccount = await connection.getAccountInfo(proposalPda);
      if (proposalAccount) {
        txResult.proposalExists = true;
        results.summary.proposalExists++;
        
        try {
          const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
          const approved = proposalData.approved || [];
          
          // 推断创建者
          if (proposalData.creator) {
            txResult.creator = proposalData.creator.toBase58();
          } else if (approved.length > 0) {
            txResult.creator = approved[0].toBase58();
          }
          
          // 获取创建时间
          try {
            const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 10 });
            if (signatures.length > 0) {
              const oldestSig = signatures[signatures.length - 1];
              txResult.createdAt = new Date(oldestSig.blockTime * 1000).toISOString();
            }
          } catch (sigErr) {
            console.log(`  ❌ 获取签名失败: ${sigErr.message}`);
          }
          
        } catch (parseErr) {
          console.log(`  ❌ 解析提案失败: ${parseErr.message}`);
        }
      }
      
      // 检查交易账户
      const [transactionPda] = multisig.getTransactionPda({
        multisigPda: multisigPubkey,
        transactionIndex: BigInt(txIndex),
        programId: MULTISIG_PROGRAM_ID
      });
      
      const transactionAccount = await connection.getAccountInfo(transactionPda);
      if (transactionAccount) {
        txResult.transactionExists = true;
        results.summary.transactionExists++;
        console.log(`  ✅ 交易账户存在!`);
        
        try {
          const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
          
          if (vaultTransactionData.message && vaultTransactionData.message.instructions) {
            const message = vaultTransactionData.message;
            
            // 分析指令寻找转账
            for (const instruction of message.instructions) {
              const programId = message.accountKeys[instruction.programIdIndex];
              
              if (programId.toBase58() === '11111111111111111111111111111112') {
                // 系统程序 - SOL转账
                if (instruction.data && instruction.data.length >= 12) {
                  const dataView = new DataView(instruction.data.buffer);
                  const instructionType = dataView.getUint32(0, true);
                  
                  if (instructionType === 2) { // Transfer
                    const lamports = dataView.getBigUint64(4, true);
                    const sol = Number(lamports) / **********;
                    const fromAccount = message.accountKeys[instruction.accounts[0]];
                    const toAccount = message.accountKeys[instruction.accounts[1]];
                    
                    txResult.hasTransferData = true;
                    txResult.transferInfo = {
                      type: 'SOL',
                      amount: sol,
                      fromAddress: fromAccount.toBase58(),
                      toAddress: toAccount.toBase58()
                    };
                    
                    results.summary.hasTransferData++;
                    console.log(`  💰 发现SOL转账: ${sol} SOL`);
                    console.log(`  📥 到: ${toAccount.toBase58().slice(0,8)}...`);
                    break;
                  }
                }
              } else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                // Token程序 - Token转账
                if (instruction.data && instruction.data.length > 0) {
                  const instructionTypeToken = instruction.data[0];
                  
                  if (instructionTypeToken === 3) { // Transfer
                    if (instruction.data.length >= 9) {
                      const dataView = new DataView(instruction.data.buffer);
                      const amount = dataView.getBigUint64(1, true);
                      const sourceAccount = message.accountKeys[instruction.accounts[0]];
                      const destAccount = message.accountKeys[instruction.accounts[1]];
                      
                      txResult.hasTransferData = true;
                      txResult.transferInfo = {
                        type: 'TOKEN',
                        amount: Number(amount),
                        fromAddress: sourceAccount.toBase58(),
                        toAddress: destAccount.toBase58()
                      };
                      
                      results.summary.hasTransferData++;
                      console.log(`  🪙 发现Token转账: ${amount} (原始单位)`);
                      console.log(`  📥 到: ${destAccount.toBase58().slice(0,8)}...`);
                      break;
                    }
                  }
                }
              }
            }
          }
          
        } catch (parseErr) {
          console.log(`  ❌ 解析交易数据失败: ${parseErr.message}`);
        }
      }
      
      results.transactions.push(txResult);
      
      // 显示结果
      const status = txResult.proposalExists ? '✅' : '❌';
      const txStatus = txResult.transactionExists ? '✅' : '❌';
      const transferStatus = txResult.hasTransferData ? '💰' : '❌';
      console.log(`  ${status} 提案 ${txStatus} 交易 ${transferStatus} 转账数据`);
      
      // 添加延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  // 保存结果
  fs.writeFileSync('multiple-transactions-test.json', JSON.stringify(results, null, 2));
  
  // 显示总结
  console.log('\n📊 测试总结:');
  console.log(`  - 总测试数: ${results.summary.totalTested}`);
  console.log(`  - 提案存在: ${results.summary.proposalExists}`);
  console.log(`  - 交易存在: ${results.summary.transactionExists}`);
  console.log(`  - 有转账数据: ${results.summary.hasTransferData}`);
  
  if (results.summary.hasTransferData > 0) {
    console.log('\n🎉 找到了包含转账数据的交易!');
    const transferTxs = results.transactions.filter(tx => tx.hasTransferData);
    transferTxs.forEach(tx => {
      console.log(`  交易 #${tx.transactionIndex}: ${tx.transferInfo.amount} ${tx.transferInfo.type} → ${tx.transferInfo.toAddress.slice(0,8)}...`);
    });
  } else {
    console.log('\n😞 没有找到包含转账数据的交易');
  }
}

testMultipleTransactions().then(() => {
  console.log('\n✅ 测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

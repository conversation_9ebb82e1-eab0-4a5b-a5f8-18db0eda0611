const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

// 配置
const VAULT_ADDRESS = "EsvC81UWiKC3DPuqZwKYqYavqEg15U7ZUKLhrgNaa5Br";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function simpleVaultTest() {
  console.log('🔍 简化金库测试');
  console.log(`🏦 金库地址: ${VAULT_ADDRESS}`);

  try {
    const vaultPubkey = new PublicKey(VAULT_ADDRESS);

    // 获取签名
    const signatures = await connection.getSignaturesForAddress(vaultPubkey, { limit: 3 });
    console.log(`✅ 找到 ${signatures.length} 个签名`);

    if (signatures.length > 0) {
      // 尝试分析多个签名
      for (let sigIndex = 0; sigIndex < Math.min(signatures.length, 3); sigIndex++) {
        const sig = signatures[sigIndex];
        console.log(`\n🔍 分析签名 ${sigIndex + 1}: ${sig.signature.slice(0, 8)}...`);

        const txInfo = await connection.getTransaction(sig.signature, {
          maxSupportedTransactionVersion: 0
        });

      if (txInfo && txInfo.transaction) {
        console.log('✅ 获取到交易信息');
        console.log('📋 交易结构:', Object.keys(txInfo.transaction));

        let message;
        if (txInfo.transaction.message) {
          message = txInfo.transaction.message;
        } else if (txInfo.transaction.transaction && txInfo.transaction.transaction.message) {
          message = txInfo.transaction.transaction.message;
        } else {
          console.log('❌ 无法找到消息结构');
          console.log('📋 交易内容:', JSON.stringify(txInfo.transaction, null, 2).slice(0, 500));
          return;
        }

        // 使用 compiledInstructions 而不是 instructions
        const instructions = message.compiledInstructions || message.instructions;
        if (!instructions) {
          console.log('❌ 消息中没有指令');
          console.log('📋 消息结构:', Object.keys(message));
          return;
        }

        console.log(`✅ 指令数: ${instructions.length}`);
        console.log(`✅ 账户数: ${message.staticAccountKeys.length}`);

        // 分析所有指令
        for (let i = 0; i < instructions.length; i++) {
          const instruction = instructions[i];
          const programId = message.staticAccountKeys[instruction.programIdIndex];
          console.log(`\n📋 指令 ${i + 1}: ${programId.toBase58().slice(0, 8)}...`);

          if (programId.toBase58() === '11111111111111111111111111111112') {
            console.log('🎯 系统程序指令');

            if (instruction.data && instruction.data.length >= 12) {
              const dataView = new DataView(instruction.data.buffer);
              const instructionType = dataView.getUint32(0, true);

              if (instructionType === 2) {
                const lamports = dataView.getBigUint64(4, true);
                const sol = Number(lamports) / **********;

                const fromAccount = message.staticAccountKeys[instruction.accountKeyIndexes[0]];
                const toAccount = message.staticAccountKeys[instruction.accountKeyIndexes[1]];

                console.log('🎉 发现SOL转账！');
                console.log(`💰 金额: ${sol} SOL`);
                console.log(`📤 从: ${fromAccount.toBase58()}`);
                console.log(`📥 到: ${toAccount.toBase58()}`);
                console.log(`📅 时间: ${new Date(sig.blockTime * 1000).toISOString()}`);

                // 这就是我们需要的数据！
                console.log('\n✅ 成功获取转账信息！');
                console.log('🎯 可以获取到：转账金额、目标地址、Token类型、交易类型');
                return; // 找到转账后退出
              }
            }
          } else {
            console.log(`🔍 其他程序: ${programId.toBase58()}`);
          }
        }

        // 添加延迟避免API限制
        if (sigIndex < Math.min(signatures.length, 3) - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } // 结束 for 循环
    } // 结束 if (signatures.length > 0)

  } catch (error) {
    console.error('❌ 错误:', error.message);
  }
}

simpleVaultTest();

const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function enhancedTest() {
  console.log('🔍 基于源代码分析的增强测试...');
  
  const results = {
    multisigAddress: MULTISIG_ADDRESS,
    exportTime: new Date().toISOString(),
    findings: []
  };

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
    
    const currentIndex = Number(multisigData.transactionIndex.toString());
    console.log('当前交易索引:', currentIndex);
    
    // 测试最新的交易
    console.log(`\n🔍 测试交易 #${currentIndex}...`);
    
    // 1. 获取提案信息（包含时间戳）
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(currentIndex),
      programId: MULTISIG_PROGRAM_ID
    });
    
    console.log('📍 提案PDA:', proposalPda.toBase58());
    
    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (proposalAccount) {
      console.log('✅ 提案账户存在');
      
      const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
      console.log('✅ 提案数据解析成功');
      console.log('  - 状态:', proposalData.status.__kind);
      
      // 🎯 关键发现：提案状态包含时间戳！
      let createdAt = null;
      if (proposalData.status.timestamp) {
        // 时间戳是 Unix 时间戳（秒）
        const timestamp = Number(proposalData.status.timestamp.toString());
        createdAt = new Date(timestamp * 1000).toISOString();
        console.log('  - 🎯 状态时间戳:', createdAt);
      }
      
      // 获取创建者
      let creator = 'Unknown';
      if (proposalData.approved && proposalData.approved.length > 0) {
        creator = proposalData.approved[0].toBase58();
        console.log('  - 创建者（推断）:', creator);
      }
      
      results.findings.push({
        type: 'proposal_with_timestamp',
        transactionIndex: currentIndex,
        status: proposalData.status.__kind,
        timestamp: proposalData.status.timestamp ? proposalData.status.timestamp.toString() : null,
        createdAt,
        creator,
        approved: proposalData.approved.map(p => p.toBase58()),
        rejected: proposalData.rejected.map(p => p.toBase58())
      });
    }
    
    // 2. 获取交易账户信息（包含完整消息）
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(currentIndex),
      programId: MULTISIG_PROGRAM_ID
    });
    
    console.log('📍 交易PDA:', transactionPda.toBase58());
    
    const transactionAccount = await connection.getAccountInfo(transactionPda);
    if (transactionAccount) {
      console.log('✅ 交易账户存在！');
      
      try {
        const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
        console.log('✅ 交易数据解析成功');
        console.log('  - 创建者:', vaultTransactionData.creator.toBase58());
        console.log('  - 交易索引:', vaultTransactionData.index.toString());
        
        // 🎯 关键发现：交易消息包含完整的指令信息！
        if (vaultTransactionData.message) {
          const message = vaultTransactionData.message;
          console.log('  - 账户数量:', message.accountKeys.length);
          console.log('  - 指令数量:', message.instructions.length);
          
          console.log('\n📋 账户列表:');
          message.accountKeys.forEach((key, idx) => {
            console.log(`    ${idx}: ${key.toBase58()}`);
          });
          
          console.log('\n🔧 指令分析:');
          message.instructions.forEach((instruction, idx) => {
            const programId = message.accountKeys[instruction.programIdIndex];
            console.log(`  指令 ${idx + 1}:`);
            console.log(`    - 程序ID: ${programId.toBase58()}`);
            console.log(`    - 账户索引: [${Array.from(instruction.accountIndexes).join(', ')}]`);
            console.log(`    - 数据长度: ${instruction.data.length} bytes`);
            
            // 分析转账指令
            if (programId.toBase58() === '11111111111111111111111111111112') {
              console.log('    - 🎯 系统程序指令（SOL转账）');
              
              if (instruction.data.length >= 12) {
                try {
                  const dataView = new DataView(instruction.data.buffer);
                  const instructionType = dataView.getUint32(0, true);
                  
                  if (instructionType === 2) { // Transfer
                    const lamports = dataView.getBigUint64(4, true);
                    const sol = Number(lamports) / **********;
                    
                    const accountIndexes = Array.from(instruction.accountIndexes);
                    const fromAccount = message.accountKeys[accountIndexes[0]];
                    const toAccount = message.accountKeys[accountIndexes[1]];
                    
                    console.log(`    - 💰 转账金额: ${sol} SOL`);
                    console.log(`    - 📤 从: ${fromAccount.toBase58()}`);
                    console.log(`    - 📥 到: ${toAccount.toBase58()}`);
                    
                    results.findings.push({
                      type: 'sol_transfer_found',
                      transactionIndex: currentIndex,
                      amount: sol,
                      token: 'SOL',
                      fromAddress: fromAccount.toBase58(),
                      toAddress: toAccount.toBase58(),
                      creator: vaultTransactionData.creator.toBase58()
                    });
                  }
                } catch (parseErr) {
                  console.log(`    - ❌ 解析转账数据失败: ${parseErr.message}`);
                }
              }
            } else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
              console.log('    - 🎯 Token程序指令');
              
              if (instruction.data.length > 0) {
                const instructionTypeToken = instruction.data[0];
                
                if (instructionTypeToken === 3) { // Transfer
                  if (instruction.data.length >= 9) {
                    try {
                      const dataView = new DataView(instruction.data.buffer);
                      const amount = dataView.getBigUint64(1, true);
                      
                      const accountIndexes = Array.from(instruction.accountIndexes);
                      const sourceAccount = message.accountKeys[accountIndexes[0]];
                      const destAccount = message.accountKeys[accountIndexes[1]];
                      
                      console.log(`    - 🪙 Token转账: ${amount} (原始单位)`);
                      console.log(`    - 📤 从: ${sourceAccount.toBase58()}`);
                      console.log(`    - 📥 到: ${destAccount.toBase58()}`);
                      
                      results.findings.push({
                        type: 'token_transfer_found',
                        transactionIndex: currentIndex,
                        amount: Number(amount),
                        fromAddress: sourceAccount.toBase58(),
                        toAddress: destAccount.toBase58(),
                        creator: vaultTransactionData.creator.toBase58()
                      });
                    } catch (parseErr) {
                      console.log(`    - ❌ 解析Token转账失败: ${parseErr.message}`);
                    }
                  }
                }
              }
            } else if (programId.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
              console.log('    - 🎯 Squads多签程序指令');
            } else {
              console.log(`    - 🔍 其他程序: ${programId.toBase58().slice(0, 8)}...`);
            }
          });
          
          results.findings.push({
            type: 'transaction_message_found',
            transactionIndex: currentIndex,
            creator: vaultTransactionData.creator.toBase58(),
            accountCount: message.accountKeys.length,
            instructionCount: message.instructions.length,
            accounts: message.accountKeys.map(key => key.toBase58()),
            instructions: message.instructions.map(inst => ({
              programIdIndex: inst.programIdIndex,
              programId: message.accountKeys[inst.programIdIndex].toBase58(),
              accountIndexes: Array.from(inst.accountIndexes),
              dataLength: inst.data.length
            }))
          });
          
        } else {
          console.log('  - ❌ 交易消息不存在');
        }
        
      } catch (parseErr) {
        console.log('❌ 解析交易数据失败:', parseErr.message);
        results.findings.push({
          type: 'error',
          message: `Parse transaction failed: ${parseErr.message}`
        });
      }
    } else {
      console.log('❌ 交易账户不存在');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    results.findings.push({
      type: 'error',
      message: error.message
    });
  }
  
  // 保存结果
  fs.writeFileSync('enhanced-test-results.json', JSON.stringify(results, null, 2));
  
  // 显示总结
  console.log('\n📊 测试总结:');
  results.findings.forEach((finding, idx) => {
    console.log(`  ${idx + 1}. ${finding.type}`);
    if (finding.type === 'sol_transfer_found') {
      console.log(`     💰 ${finding.amount} SOL → ${finding.toAddress.slice(0,8)}...`);
    } else if (finding.type === 'token_transfer_found') {
      console.log(`     🪙 ${finding.amount} Token → ${finding.toAddress.slice(0,8)}...`);
    } else if (finding.type === 'proposal_with_timestamp') {
      console.log(`     📅 ${finding.createdAt || '无时间戳'}`);
    }
  });
  
  const hasTransferData = results.findings.some(f => f.type.includes('transfer_found'));
  const hasTimestamp = results.findings.some(f => f.type === 'proposal_with_timestamp' && f.createdAt);
  
  console.log('\n🎯 关键结论:');
  console.log(`  - 可获取转账数据: ${hasTransferData ? '✅ 是' : '❌ 否'}`);
  console.log(`  - 可获取创建时间: ${hasTimestamp ? '✅ 是' : '❌ 否'}`);
}

enhancedTest().then(() => {
  console.log('\n🎉 增强测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

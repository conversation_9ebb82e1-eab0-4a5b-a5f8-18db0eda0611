const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function deepAnalysisMultisig() {
  console.log('🔍 深度分析多签地址 - 尝试所有可能的方法');
  console.log(`📍 目标地址: ${MULTISIG_ADDRESS}`);
  console.log('=' .repeat(80));
  
  const results = {
    multisigAddress: MULTISIG_ADDRESS,
    exportTime: new Date().toISOString(),
    methods: []
  };

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
    
    console.log('✅ 多签基本信息:');
    console.log(`  - 当前交易索引: ${multisigData.transactionIndex.toString()}`);
    console.log(`  - 成员数量: ${multisigData.members.length}`);
    console.log(`  - 阈值: ${multisigData.threshold}`);
    console.log(`  - 创建者: ${multisigData.createKey.toBase58()}`);
    
    const currentIndex = Number(multisigData.transactionIndex.toString());
    
    // === 方法1: 尝试所有交易索引 ===
    console.log('\n🔍 方法1: 遍历所有交易索引寻找交易数据');
    console.log('-'.repeat(60));
    
    const method1Result = {
      name: 'traverse_all_transactions',
      success: false,
      foundTransactions: [],
      errors: []
    };
    
    // 测试从1到当前索引的所有交易
    for (let i = Math.max(1, currentIndex - 10); i <= currentIndex; i++) {
      console.log(`  🔍 检查交易 #${i}...`);
      
      try {
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });
        
        const transactionAccount = await connection.getAccountInfo(transactionPda);
        if (transactionAccount) {
          console.log(`    ✅ 找到交易账户！`);
          
          try {
            const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
            
            const transactionInfo = {
              index: i,
              creator: vaultTransactionData.creator.toBase58(),
              vaultIndex: vaultTransactionData.vaultIndex,
              hasMessage: !!vaultTransactionData.message,
              messageDetails: null
            };
            
            if (vaultTransactionData.message) {
              const message = vaultTransactionData.message;
              console.log(`    📨 消息详情:`);
              console.log(`      - 账户数: ${message.accountKeys.length}`);
              console.log(`      - 指令数: ${message.instructions.length}`);
              
              transactionInfo.messageDetails = {
                accountCount: message.accountKeys.length,
                instructionCount: message.instructions.length,
                accounts: message.accountKeys.map(key => key.toBase58()),
                instructions: []
              };
              
              // 分析每个指令
              message.instructions.forEach((instruction, idx) => {
                const programId = message.accountKeys[instruction.programIdIndex];
                console.log(`      - 指令 ${idx + 1}: ${programId.toBase58().slice(0, 8)}...`);
                
                const instructionInfo = {
                  index: idx,
                  programId: programId.toBase58(),
                  programIdIndex: instruction.programIdIndex,
                  accountIndexes: Array.from(instruction.accountIndexes),
                  dataLength: instruction.data.length,
                  transferInfo: null
                };
                
                // 解析SOL转账
                if (programId.toBase58() === '11111111111111111111111111111112') {
                  console.log(`        🎯 系统程序指令`);
                  
                  if (instruction.data.length >= 12) {
                    try {
                      const dataView = new DataView(instruction.data.buffer);
                      const instructionType = dataView.getUint32(0, true);
                      
                      if (instructionType === 2) {
                        const lamports = dataView.getBigUint64(4, true);
                        const sol = Number(lamports) / **********;
                        
                        const accountIndexes = Array.from(instruction.accountIndexes);
                        const fromAccount = message.accountKeys[accountIndexes[0]];
                        const toAccount = message.accountKeys[accountIndexes[1]];
                        
                        instructionInfo.transferInfo = {
                          type: 'SOL',
                          amount: sol,
                          fromAddress: fromAccount.toBase58(),
                          toAddress: toAccount.toBase58()
                        };
                        
                        console.log(`        💰 SOL转账: ${sol} SOL → ${toAccount.toBase58().slice(0, 8)}...`);
                      }
                    } catch (parseErr) {
                      console.log(`        ❌ 解析失败: ${parseErr.message}`);
                    }
                  }
                }
                
                // 解析Token转账
                else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                  console.log(`        🎯 Token程序指令`);
                  
                  if (instruction.data.length > 0 && instruction.data[0] === 3) {
                    if (instruction.data.length >= 9) {
                      try {
                        const dataView = new DataView(instruction.data.buffer);
                        const amount = dataView.getBigUint64(1, true);
                        
                        const accountIndexes = Array.from(instruction.accountIndexes);
                        const sourceAccount = message.accountKeys[accountIndexes[0]];
                        const destAccount = message.accountKeys[accountIndexes[1]];
                        
                        instructionInfo.transferInfo = {
                          type: 'TOKEN',
                          amount: Number(amount),
                          fromAddress: sourceAccount.toBase58(),
                          toAddress: destAccount.toBase58()
                        };
                        
                        console.log(`        🪙 Token转账: ${amount} → ${destAccount.toBase58().slice(0, 8)}...`);
                      } catch (parseErr) {
                        console.log(`        ❌ Token解析失败: ${parseErr.message}`);
                      }
                    }
                  }
                }
                
                transactionInfo.messageDetails.instructions.push(instructionInfo);
              });
            }
            
            method1Result.foundTransactions.push(transactionInfo);
            method1Result.success = true;
            
          } catch (parseErr) {
            console.log(`    ❌ 解析交易数据失败: ${parseErr.message}`);
            method1Result.errors.push(`Parse tx ${i}: ${parseErr.message}`);
          }
        } else {
          console.log(`    ❌ 交易账户不存在`);
        }
      } catch (err) {
        console.log(`    ❌ 检查交易 ${i} 失败: ${err.message}`);
        method1Result.errors.push(`Check tx ${i}: ${err.message}`);
      }
      
      // 添加延迟避免API限制
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    results.methods.push(method1Result);
    
    // === 方法2: 通过多签地址的所有签名历史查找 ===
    console.log('\n🔍 方法2: 通过多签地址签名历史查找转账');
    console.log('-'.repeat(60));
    
    const method2Result = {
      name: 'multisig_signature_history',
      success: false,
      signatures: [],
      transferTransactions: [],
      errors: []
    };
    
    try {
      console.log('  📅 获取多签地址的签名历史...');
      const multisigSignatures = await connection.getSignaturesForAddress(multisigPubkey, { limit: 20 });
      
      console.log(`  找到 ${multisigSignatures.length} 个签名`);
      method2Result.signatures = multisigSignatures.map(sig => ({
        signature: sig.signature,
        blockTime: sig.blockTime,
        timeISO: new Date(sig.blockTime * 1000).toISOString()
      }));
      
      // 分析每个签名对应的交易
      for (let i = 0; i < Math.min(multisigSignatures.length, 5); i++) {
        const sig = multisigSignatures[i];
        console.log(`  🔍 分析签名 ${i + 1}: ${sig.signature.slice(0, 8)}...`);
        
        try {
          const txInfo = await connection.getTransaction(sig.signature, {
            maxSupportedTransactionVersion: 0
          });
          
          if (txInfo && txInfo.transaction && txInfo.transaction.message) {
            const message = txInfo.transaction.message;
            console.log(`    ✅ 获取到交易，指令数: ${message.instructions.length}`);
            
            // 分析指令寻找转账
            message.instructions.forEach((instruction, idx) => {
              const programId = message.accountKeys[instruction.programIdIndex];
              
              if (programId.toBase58() === '11111111111111111111111111111112') {
                console.log(`    🎯 发现系统程序指令 ${idx + 1}`);
                
                if (instruction.data && instruction.data.length >= 12) {
                  try {
                    const dataView = new DataView(instruction.data.buffer);
                    const instructionType = dataView.getUint32(0, true);
                    
                    if (instructionType === 2) {
                      const lamports = dataView.getBigUint64(4, true);
                      const sol = Number(lamports) / **********;
                      
                      const fromAccount = message.accountKeys[instruction.accounts[0]];
                      const toAccount = message.accountKeys[instruction.accounts[1]];
                      
                      console.log(`      💰 SOL转账: ${sol} SOL`);
                      console.log(`      📤 从: ${fromAccount.toBase58()}`);
                      console.log(`      📥 到: ${toAccount.toBase58()}`);
                      
                      method2Result.transferTransactions.push({
                        signature: sig.signature,
                        blockTime: sig.blockTime,
                        type: 'SOL',
                        amount: sol,
                        fromAddress: fromAccount.toBase58(),
                        toAddress: toAccount.toBase58()
                      });
                      
                      method2Result.success = true;
                    }
                  } catch (parseErr) {
                    console.log(`      ❌ 解析失败: ${parseErr.message}`);
                  }
                }
              }
            });
          }
        } catch (txErr) {
          console.log(`    ❌ 获取交易失败: ${txErr.message}`);
          method2Result.errors.push(`Get tx ${sig.signature.slice(0, 8)}: ${txErr.message}`);
        }
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (err) {
      console.log(`❌ 方法2失败: ${err.message}`);
      method2Result.errors.push(err.message);
    }
    
    results.methods.push(method2Result);
    
    // === 方法3: 查找 Vault 地址的交易历史 ===
    console.log('\n🔍 方法3: 查找 Vault 地址的交易历史');
    console.log('-'.repeat(60));
    
    const method3Result = {
      name: 'vault_transaction_history',
      success: false,
      vaults: [],
      errors: []
    };
    
    try {
      // 获取所有可能的 vault 地址
      for (let vaultIndex = 0; vaultIndex < 5; vaultIndex++) {
        console.log(`  🔍 检查 Vault #${vaultIndex}...`);
        
        try {
          const [vaultPda] = multisig.getVaultPda({
            multisigPda: multisigPubkey,
            index: vaultIndex,
            programId: MULTISIG_PROGRAM_ID
          });
          
          console.log(`    📍 Vault PDA: ${vaultPda.toBase58()}`);
          
          // 获取 vault 的签名历史
          const vaultSignatures = await connection.getSignaturesForAddress(vaultPda, { limit: 10 });
          
          if (vaultSignatures.length > 0) {
            console.log(`    ✅ 找到 ${vaultSignatures.length} 个 Vault 签名`);
            
            const vaultInfo = {
              index: vaultIndex,
              address: vaultPda.toBase58(),
              signatureCount: vaultSignatures.length,
              signatures: vaultSignatures.map(sig => ({
                signature: sig.signature,
                blockTime: sig.blockTime,
                timeISO: new Date(sig.blockTime * 1000).toISOString()
              })),
              transferTransactions: []
            };
            
            // 分析 vault 的交易
            for (const sig of vaultSignatures.slice(0, 3)) {
              try {
                const txInfo = await connection.getTransaction(sig.signature, {
                  maxSupportedTransactionVersion: 0
                });
                
                if (txInfo && txInfo.transaction && txInfo.transaction.message) {
                  const message = txInfo.transaction.message;
                  
                  // 寻找转账指令
                  message.instructions.forEach((instruction, idx) => {
                    const programId = message.accountKeys[instruction.programIdIndex];
                    
                    if (programId.toBase58() === '11111111111111111111111111111112') {
                      if (instruction.data && instruction.data.length >= 12) {
                        try {
                          const dataView = new DataView(instruction.data.buffer);
                          const instructionType = dataView.getUint32(0, true);
                          
                          if (instructionType === 2) {
                            const lamports = dataView.getBigUint64(4, true);
                            const sol = Number(lamports) / **********;
                            
                            const fromAccount = message.accountKeys[instruction.accounts[0]];
                            const toAccount = message.accountKeys[instruction.accounts[1]];
                            
                            console.log(`      💰 Vault转账: ${sol} SOL → ${toAccount.toBase58().slice(0, 8)}...`);
                            
                            vaultInfo.transferTransactions.push({
                              signature: sig.signature,
                              type: 'SOL',
                              amount: sol,
                              fromAddress: fromAccount.toBase58(),
                              toAddress: toAccount.toBase58()
                            });
                            
                            method3Result.success = true;
                          }
                        } catch (parseErr) {
                          // 忽略解析错误
                        }
                      }
                    }
                  });
                }
              } catch (txErr) {
                // 忽略交易获取错误
              }
              
              await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            method3Result.vaults.push(vaultInfo);
          } else {
            console.log(`    ❌ 没有找到 Vault 签名`);
          }
        } catch (vaultErr) {
          console.log(`    ❌ 检查 Vault ${vaultIndex} 失败: ${vaultErr.message}`);
          method3Result.errors.push(`Vault ${vaultIndex}: ${vaultErr.message}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (err) {
      console.log(`❌ 方法3失败: ${err.message}`);
      method3Result.errors.push(err.message);
    }
    
    results.methods.push(method3Result);
    
  } catch (error) {
    console.error('❌ 深度分析失败:', error.message);
    results.error = error.message;
  }
  
  // 保存结果
  fs.writeFileSync('deep-analysis-results.json', JSON.stringify(results, null, 2));
  
  // 显示总结
  console.log('\n📊 深度分析总结:');
  console.log('=' .repeat(80));
  
  const successfulMethods = results.methods.filter(m => m.success);
  const foundTransfers = [];
  
  results.methods.forEach(method => {
    if (method.foundTransactions) {
      method.foundTransactions.forEach(tx => {
        if (tx.messageDetails && tx.messageDetails.instructions) {
          tx.messageDetails.instructions.forEach(inst => {
            if (inst.transferInfo) {
              foundTransfers.push({
                method: method.name,
                ...inst.transferInfo
              });
            }
          });
        }
      });
    }
    
    if (method.transferTransactions) {
      method.transferTransactions.forEach(tx => {
        foundTransfers.push({
          method: method.name,
          ...tx
        });
      });
    }
    
    if (method.vaults) {
      method.vaults.forEach(vault => {
        vault.transferTransactions.forEach(tx => {
          foundTransfers.push({
            method: method.name,
            vault: vault.index,
            ...tx
          });
        });
      });
    }
  });
  
  console.log(`✅ 成功方法数: ${successfulMethods.length}/${results.methods.length}`);
  console.log(`💰 找到转账记录: ${foundTransfers.length}`);
  
  if (foundTransfers.length > 0) {
    console.log('\n🎉 找到的转账信息:');
    foundTransfers.forEach((transfer, idx) => {
      console.log(`  ${idx + 1}. [${transfer.method}] ${transfer.amount} ${transfer.type} → ${transfer.toAddress?.slice(0, 8)}...`);
    });
  } else {
    console.log('\n😞 没有找到转账信息');
    console.log('💡 可能的原因:');
    console.log('  1. 这个多签地址使用了不同的交易结构');
    console.log('  2. 转账数据存储在其他位置');
    console.log('  3. 需要使用 Squads 平台的专用 API');
  }
  
  console.log(`\n📄 详细结果已保存到: deep-analysis-results.json`);
}

deepAnalysisMultisig().then(() => {
  console.log('\n🎉 深度分析完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 分析异常:', err);
  process.exit(1);
});

const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function simpleTest() {
  console.log('🔍 简单测试多签数据获取');
  
  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
    
    console.log('✅ 多签基本信息:');
    console.log('  - 当前交易索引:', multisigData.transactionIndex.toString());
    console.log('  - 创建者:', multisigData.createKey.toBase58());
    console.log('  - 成员:', multisigData.members.map(m => m.key.toBase58()));
    
    // 获取最新提案
    const currentIndex = Number(multisigData.transactionIndex.toString());
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(currentIndex),
      programId: MULTISIG_PROGRAM_ID
    });
    
    console.log('\n📍 最新提案 PDA:', proposalPda.toBase58());
    
    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (proposalAccount) {
      const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
      console.log('✅ 提案信息:');
      console.log('  - 状态:', proposalData.status.__kind);
      console.log('  - 批准者:', proposalData.approved.map(p => p.toBase58()));
      
      // 获取签名历史（只获取最新的一个）
      const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 1 });
      if (signatures.length > 0) {
        const sig = signatures[0];
        console.log('\n📅 最新签名:');
        console.log('  - 时间:', new Date(sig.blockTime * 1000).toISOString());
        console.log('  - 签名:', sig.signature);
        
        // 这就是我们能获取到的真实数据
        console.log('\n🎯 可用于前端的数据:');
        console.log('  - 交易索引:', currentIndex);
        console.log('  - 交易类型: "unknown" (无法从现有数据推断)');
        console.log('  - 转账金额: null (无法从现有数据获取)');
        console.log('  - 转账Token: null (无法从现有数据获取)');
        console.log('  - 目标地址: null (无法从现有数据获取)');
        console.log('  - 创建时间:', new Date(sig.blockTime * 1000).toISOString());
        console.log('  - 创建者:', proposalData.approved.length > 0 ? proposalData.approved[0].toBase58() : '未知');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

simpleTest().then(() => {
  console.log('\n✅ 测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

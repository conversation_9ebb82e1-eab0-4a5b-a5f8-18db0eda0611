const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

// 输出文件
const OUTPUT_FILE = 'transaction-data-export.json';

async function exportTransactionData() {
  console.log('🔍 开始导出交易数据...');

  const exportData = {
    multisigAddress: MULTISIG_ADDRESS,
    exportTime: new Date().toISOString(),
    multisigInfo: null,
    transactions: []
  };

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    exportData.multisigInfo = {
      address: MULTISIG_ADDRESS,
      threshold: multisigData.threshold,
      members: multisigData.members.map(m => ({
        key: m.key.toBase58(),
        permissions: m.permissions || {}
      })),
      createKey: multisigData.createKey.toBase58(),
      transactionIndex: multisigData.transactionIndex ? multisigData.transactionIndex.toString() : '0',
      msChangeIndex: multisigData.msChangeIndex ? multisigData.msChangeIndex.toString() : '0',
      bump: multisigData.bump || 0
    };

    console.log('✅ 多签基本信息获取成功');
    console.log('  - 当前交易索引:', multisigData.transactionIndex ? multisigData.transactionIndex.toString() : '0');
    console.log('  - 成员数量:', multisigData.members.length);

    // 分析最近的几个交易
    const currentIndex = Number(multisigData.transactionIndex ? multisigData.transactionIndex.toString() : '0');
    const testIndexes = [];

    // 获取最近5个交易索引
    for (let i = Math.max(1, currentIndex - 4); i <= currentIndex; i++) {
      testIndexes.push(i);
    }

    console.log('📋 将分析交易索引:', testIndexes);

    for (const txIndex of testIndexes) {
      console.log(`\n🔍 分析交易 #${txIndex}...`);

      const transactionData = {
        transactionIndex: txIndex,
        proposalData: null,
        transactionAccountData: null,
        signatures: [],
        error: null
      };

      try {
        // 1. 获取提案信息
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(txIndex),
          programId: MULTISIG_PROGRAM_ID
        });

        console.log(`  📍 提案PDA: ${proposalPda.toBase58()}`);

        const proposalAccount = await connection.getAccountInfo(proposalPda);
        if (proposalAccount) {
          const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];

          transactionData.proposalData = {
            creator: proposalData.creator ? proposalData.creator.toBase58() : null,
            status: proposalData.status,
            approved: proposalData.approved.map(p => p.toBase58()),
            rejected: proposalData.rejected.map(p => p.toBase58()),
            cancelled: proposalData.cancelled.map(p => p.toBase58()),
            executed: proposalData.executed.map(p => p.toBase58())
          };

          console.log(`  ✅ 提案状态: ${proposalData.status.__kind}`);
          console.log(`  👥 批准者: ${proposalData.approved.length}人`);
        } else {
          console.log('  ❌ 提案账户不存在');
        }

        // 2. 获取交易账户信息
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(txIndex),
          programId: MULTISIG_PROGRAM_ID
        });

        console.log(`  📍 交易PDA: ${transactionPda.toBase58()}`);

        const transactionAccount = await connection.getAccountInfo(transactionPda);
        if (transactionAccount) {
          try {
            const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];

            transactionData.transactionAccountData = {
              creator: vaultTransactionData.creator ? vaultTransactionData.creator.toBase58() : null,
              memo: vaultTransactionData.memo || null,
              ephemeralSigners: vaultTransactionData.ephemeralSigners,
              message: null // 我们将详细分析消息
            };

            console.log(`  ✅ 交易账户存在`);
            console.log(`  📝 Memo: ${vaultTransactionData.memo || '(空)'}`);

            // 详细分析交易消息
            if (vaultTransactionData.message) {
              console.log(`  📨 交易消息存在，开始解析...`);

              const message = vaultTransactionData.message;
              const messageData = {
                accountKeys: message.accountKeys ? message.accountKeys.map(key => key.toBase58()) : [],
                instructions: [],
                recentBlockhash: message.recentBlockhash || null,
                header: message.header || null
              };

              if (message.instructions) {
                message.instructions.forEach((instruction, idx) => {
                  const instructionData = {
                    programIdIndex: instruction.programIdIndex,
                    accounts: instruction.accounts,
                    data: instruction.data ? Array.from(instruction.data) : [],
                    dataHex: instruction.data ? Buffer.from(instruction.data).toString('hex') : null
                  };

                  // 分析程序类型
                  if (messageData.accountKeys[instruction.programIdIndex]) {
                    const programId = messageData.accountKeys[instruction.programIdIndex];
                    instructionData.programId = programId;

                    if (programId === '11111111111111111111111111111112') {
                      instructionData.programType = 'System';

                      // 解析系统程序指令
                      if (instruction.data && instruction.data.length >= 12) {
                        const dataView = new DataView(instruction.data.buffer);
                        const instructionType = dataView.getUint32(0, true);

                        if (instructionType === 2) { // Transfer
                          const lamports = dataView.getBigUint64(4, true);
                          instructionData.transferInfo = {
                            type: 'SOL_TRANSFER',
                            amount: Number(lamports),
                            amountSOL: Number(lamports) / **********,
                            fromAccount: messageData.accountKeys[instruction.accounts[0]] || null,
                            toAccount: messageData.accountKeys[instruction.accounts[1]] || null
                          };

                          console.log(`    💰 发现SOL转账: ${instructionData.transferInfo.amountSOL} SOL`);
                          console.log(`    📤 从: ${instructionData.transferInfo.fromAccount?.slice(0,8)}...`);
                          console.log(`    📥 到: ${instructionData.transferInfo.toAccount?.slice(0,8)}...`);
                        }
                      }
                    } else if (programId === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                      instructionData.programType = 'Token';

                      // 解析Token指令
                      if (instruction.data && instruction.data.length > 0) {
                        const instructionTypeToken = instruction.data[0];

                        if (instructionTypeToken === 3) { // Transfer
                          if (instruction.data.length >= 9) {
                            const dataView = new DataView(instruction.data.buffer);
                            const amount = dataView.getBigUint64(1, true);

                            instructionData.transferInfo = {
                              type: 'TOKEN_TRANSFER',
                              amount: Number(amount),
                              sourceAccount: messageData.accountKeys[instruction.accounts[0]] || null,
                              destAccount: messageData.accountKeys[instruction.accounts[1]] || null,
                              authority: messageData.accountKeys[instruction.accounts[2]] || null
                            };

                            console.log(`    🪙 发现Token转账: ${amount} (原始单位)`);
                            console.log(`    📤 从: ${instructionData.transferInfo.sourceAccount?.slice(0,8)}...`);
                            console.log(`    📥 到: ${instructionData.transferInfo.destAccount?.slice(0,8)}...`);
                          }
                        }
                      }
                    } else if (programId === MULTISIG_PROGRAM_ID.toBase58()) {
                      instructionData.programType = 'Squads';
                    } else {
                      instructionData.programType = 'Other';
                    }
                  }

                  messageData.instructions.push(instructionData);
                });
              }

              transactionData.transactionAccountData.message = messageData;
            }

          } catch (parseErr) {
            console.log(`  ❌ 解析交易账户失败: ${parseErr.message}`);
            transactionData.error = `Parse transaction account failed: ${parseErr.message}`;
          }
        } else {
          console.log('  ❌ 交易账户不存在');
        }

        // 3. 获取签名历史
        try {
          const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 5 });
          transactionData.signatures = signatures.map(sig => ({
            signature: sig.signature,
            blockTime: sig.blockTime,
            confirmationStatus: sig.confirmationStatus,
            err: sig.err,
            memo: sig.memo,
            slot: sig.slot
          }));

          console.log(`  📅 找到 ${signatures.length} 个签名`);
          if (signatures.length > 0) {
            const latestTime = new Date(signatures[0].blockTime * 1000).toISOString();
            console.log(`  🕐 最新时间: ${latestTime}`);
          }
        } catch (sigErr) {
          console.log(`  ❌ 获取签名失败: ${sigErr.message}`);
          transactionData.error = `Get signatures failed: ${sigErr.message}`;
        }

      } catch (err) {
        console.log(`  💥 处理交易 #${txIndex} 失败: ${err.message}`);
        transactionData.error = err.message;
      }

      exportData.transactions.push(transactionData);

      // 添加延迟避免API限制
      if (txIndex < testIndexes[testIndexes.length - 1]) {
        console.log('  ⏳ 等待1秒...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 写入文件
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(exportData, null, 2));
    console.log(`\n✅ 数据导出完成: ${OUTPUT_FILE}`);
    console.log(`📊 导出了 ${exportData.transactions.length} 个交易的详细信息`);

  } catch (error) {
    console.error('❌ 导出失败:', error.message);
    exportData.error = error.message;
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(exportData, null, 2));
  }
}

// 运行导出
exportTransactionData().then(() => {
  console.log('\n🎉 导出任务完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 导出异常:', err);
  process.exit(1);
});

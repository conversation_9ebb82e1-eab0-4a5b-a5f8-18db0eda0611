{"multisigAddress": "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr", "exportTime": "2025-07-24T07:25:22.713Z", "multisigInfo": {"address": "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr", "threshold": 2, "members": [{"key": "2hvBjn1R3nSXHJop7dz2xTTWSLkQiHSjPmpyjdQY7p7Y", "permissions": {"mask": 6}}, {"key": "7d1AQaAoMJ2fLeJrDtfUNJMQAHA4rMYGDRkiENEzbtqJ", "permissions": {"mask": 2}}, {"key": "DX5aoYavriPUujcRzy5i5WuNpBUGAuXv7kLB1EvpUdGz", "permissions": {"mask": 1}}, {"key": "EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG", "permissions": {"mask": 7}}], "createKey": "CALJK99n9dNcBybibRDpgGQp3T1YSMk1ypP2bHuXouv1", "transactionIndex": "43", "msChangeIndex": "0", "bump": 254}, "transactions": [{"transactionIndex": 39, "proposalData": null, "transactionAccountData": null, "signatures": [], "error": "Cannot read properties of undefined (reading 'map')"}, {"transactionIndex": 40, "proposalData": null, "transactionAccountData": null, "signatures": [], "error": "Cannot read properties of undefined (reading 'map')"}, {"transactionIndex": 41, "proposalData": null, "transactionAccountData": null, "signatures": [], "error": "Cannot read properties of undefined (reading 'map')"}, {"transactionIndex": 42, "proposalData": null, "transactionAccountData": null, "signatures": [], "error": "Cannot read properties of undefined (reading 'map')"}, {"transactionIndex": 43, "proposalData": null, "transactionAccountData": null, "signatures": [], "error": "Cannot read properties of undefined (reading 'map')"}]}
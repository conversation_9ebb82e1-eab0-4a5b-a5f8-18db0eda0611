const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const fs = require('fs');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function simpleExport() {
  console.log('🔍 简化导出测试...');
  
  const results = {
    multisigAddress: MULTISIG_ADDRESS,
    exportTime: new Date().toISOString(),
    findings: []
  };

  try {
    const multisigPubkey = new PublicKey(MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);
    
    console.log('✅ 多签信息获取成功');
    console.log('  - 当前交易索引:', multisigData.transactionIndex.toString());
    
    // 只测试最新的交易
    const currentIndex = Number(multisigData.transactionIndex.toString());
    
    console.log(`\n🔍 测试交易 #${currentIndex}...`);
    
    // 获取提案PDA
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(currentIndex),
      programId: MULTISIG_PROGRAM_ID
    });
    
    console.log('📍 提案PDA:', proposalPda.toBase58());
    
    // 检查提案账户
    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (proposalAccount) {
      console.log('✅ 提案账户存在');
      
      try {
        const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
        console.log('✅ 提案数据解析成功');
        console.log('  - 状态:', proposalData.status.__kind);
        console.log('  - 创建者:', proposalData.creator ? proposalData.creator.toBase58() : '未知');
        
        // 安全地处理数组
        const approved = proposalData.approved || [];
        const rejected = proposalData.rejected || [];
        
        console.log('  - 批准者数量:', approved.length);
        console.log('  - 拒绝者数量:', rejected.length);
        
        if (approved.length > 0) {
          console.log('  - 批准者:', approved.map(p => p.toBase58()));
        }
        
        results.findings.push({
          type: 'proposal_data',
          transactionIndex: currentIndex,
          status: proposalData.status.__kind,
          creator: proposalData.creator ? proposalData.creator.toBase58() : null,
          approved: approved.map(p => p.toBase58()),
          rejected: rejected.map(p => p.toBase58())
        });
        
      } catch (parseErr) {
        console.log('❌ 解析提案数据失败:', parseErr.message);
        results.findings.push({
          type: 'error',
          message: `Parse proposal failed: ${parseErr.message}`
        });
      }
    } else {
      console.log('❌ 提案账户不存在');
    }
    
    // 检查交易账户
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(currentIndex),
      programId: MULTISIG_PROGRAM_ID
    });
    
    console.log('📍 交易PDA:', transactionPda.toBase58());
    
    const transactionAccount = await connection.getAccountInfo(transactionPda);
    if (transactionAccount) {
      console.log('✅ 交易账户存在');
      
      try {
        const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
        console.log('✅ 交易数据解析成功');
        console.log('  - 创建者:', vaultTransactionData.creator ? vaultTransactionData.creator.toBase58() : '未知');
        console.log('  - Memo:', vaultTransactionData.memo || '(空)');
        
        // 检查交易消息
        if (vaultTransactionData.message) {
          console.log('✅ 交易消息存在');
          
          const message = vaultTransactionData.message;
          console.log('  - 账户数量:', message.accountKeys ? message.accountKeys.length : 0);
          console.log('  - 指令数量:', message.instructions ? message.instructions.length : 0);
          
          if (message.accountKeys && message.instructions) {
            console.log('\n📋 账户列表:');
            message.accountKeys.forEach((key, idx) => {
              console.log(`  ${idx}: ${key.toBase58()}`);
            });
            
            console.log('\n🔧 指令分析:');
            message.instructions.forEach((instruction, idx) => {
              const programId = message.accountKeys[instruction.programIdIndex];
              console.log(`  指令 ${idx + 1}:`);
              console.log(`    - 程序ID: ${programId.toBase58()}`);
              console.log(`    - 账户索引: [${instruction.accounts.join(', ')}]`);
              console.log(`    - 数据长度: ${instruction.data ? instruction.data.length : 0} bytes`);
              
              // 分析具体指令类型
              if (programId.toBase58() === '11111111111111111111111111111112') {
                console.log('    - 🎯 系统程序指令');
                
                if (instruction.data && instruction.data.length >= 12) {
                  const dataView = new DataView(instruction.data.buffer);
                  const instructionType = dataView.getUint32(0, true);
                  
                  if (instructionType === 2) { // Transfer
                    const lamports = dataView.getBigUint64(4, true);
                    const sol = Number(lamports) / **********;
                    const fromAccount = message.accountKeys[instruction.accounts[0]];
                    const toAccount = message.accountKeys[instruction.accounts[1]];
                    
                    console.log(`    - 💰 SOL转账: ${sol} SOL`);
                    console.log(`    - 📤 从: ${fromAccount.toBase58()}`);
                    console.log(`    - 📥 到: ${toAccount.toBase58()}`);
                    
                    results.findings.push({
                      type: 'sol_transfer',
                      transactionIndex: currentIndex,
                      amount: sol,
                      token: 'SOL',
                      fromAddress: fromAccount.toBase58(),
                      toAddress: toAccount.toBase58()
                    });
                  }
                }
              } else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                console.log('    - 🎯 Token程序指令');
                
                if (instruction.data && instruction.data.length > 0) {
                  const instructionTypeToken = instruction.data[0];
                  
                  if (instructionTypeToken === 3) { // Transfer
                    if (instruction.data.length >= 9) {
                      const dataView = new DataView(instruction.data.buffer);
                      const amount = dataView.getBigUint64(1, true);
                      const sourceAccount = message.accountKeys[instruction.accounts[0]];
                      const destAccount = message.accountKeys[instruction.accounts[1]];
                      
                      console.log(`    - 🪙 Token转账: ${amount} (原始单位)`);
                      console.log(`    - 📤 从: ${sourceAccount.toBase58()}`);
                      console.log(`    - 📥 到: ${destAccount.toBase58()}`);
                      
                      results.findings.push({
                        type: 'token_transfer',
                        transactionIndex: currentIndex,
                        amount: Number(amount),
                        fromAddress: sourceAccount.toBase58(),
                        toAddress: destAccount.toBase58()
                      });
                    }
                  }
                }
              }
            });
          }
          
        } else {
          console.log('❌ 交易消息不存在');
        }
        
      } catch (parseErr) {
        console.log('❌ 解析交易数据失败:', parseErr.message);
        results.findings.push({
          type: 'error',
          message: `Parse transaction failed: ${parseErr.message}`
        });
      }
    } else {
      console.log('❌ 交易账户不存在');
    }
    
    // 获取签名历史
    try {
      const signatures = await connection.getSignaturesForAddress(proposalPda, { limit: 3 });
      console.log(`\n📅 签名历史: ${signatures.length} 个`);
      
      if (signatures.length > 0) {
        signatures.forEach((sig, idx) => {
          const time = new Date(sig.blockTime * 1000).toISOString();
          console.log(`  ${idx + 1}. ${time} - ${sig.signature.slice(0, 8)}...`);
        });
        
        results.findings.push({
          type: 'signatures',
          transactionIndex: currentIndex,
          signatures: signatures.map(sig => ({
            signature: sig.signature,
            blockTime: sig.blockTime,
            timeISO: new Date(sig.blockTime * 1000).toISOString()
          }))
        });
      }
    } catch (sigErr) {
      console.log('❌ 获取签名失败:', sigErr.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    results.findings.push({
      type: 'error',
      message: error.message
    });
  }
  
  // 写入结果
  fs.writeFileSync('simple-export-results.json', JSON.stringify(results, null, 2));
  console.log('\n✅ 结果已保存到 simple-export-results.json');
  
  // 总结发现
  console.log('\n📊 发现总结:');
  results.findings.forEach((finding, idx) => {
    console.log(`  ${idx + 1}. ${finding.type}: ${JSON.stringify(finding).slice(0, 100)}...`);
  });
}

simpleExport().then(() => {
  console.log('\n🎉 测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});

{"exportTime": "2025-07-24T07:54:59.469Z", "addressTests": [{"address": "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr", "isValidMultisig": true, "multisigInfo": {"threshold": 2, "memberCount": 4, "transactionIndex": "43", "createKey": "CALJK99n9dNcBybibRDpgGQp3T1YSMk1ypP2bHuXouv1"}, "hasTransactionData": false, "sampleTransaction": null, "error": null}, {"address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM", "isValidMultisig": false, "multisigInfo": null, "hasTransactionData": false, "sampleTransaction": null, "error": "Invalid multisig: Expected  to hold a COption"}, {"address": "DRpbCBMxVnDK7maPM5tGv6MvB3v1sRMC7Probn7KMqg", "isValidMultisig": false, "multisigInfo": null, "hasTransactionData": false, "sampleTransaction": null, "error": "Invalid multisig: Unable to find Multisig account at DRpbCBMxVnDK7maPM5tGv6MvB3v1sRMC7Probn7KMqg"}]}